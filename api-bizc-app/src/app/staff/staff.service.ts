import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateStaffMemberDto } from './dto/create-staff-member.dto';
import { UpdateStaffMemberDto } from './dto/update-staff-member.dto';
import { StaffMemberDto } from './dto/staff-member.dto';
import { StaffMemberSlimDto } from './dto/staff-member-slim.dto';
import { StaffMemberAutocompleteDto } from './dto/staff-member-autocomplete.dto';
import { StaffMemberListDto } from './dto/staff-member-list.dto';
import { AuditUserDto } from '../shared/dto/audit-user.dto';
import { PaginatedStaffResponseDto } from './dto/paginated-staff-response.dto';
import { BulkCreateStaffDto } from './dto/bulk-create-staff.dto';
import { BulkDeleteStaffDto } from './dto/bulk-delete-staff.dto';
import { BulkDeleteStaffResponseDto } from './dto/bulk-delete-staff-response.dto';
import { StaffPhysicalInfoDto } from './dto/staff-physical-info.dto';
import { AssignStaffLeaveTypeDto } from './dto/assign-staff-leave-type.dto';
import { BulkAssignStaffLeaveTypesDto } from './dto/bulk-assign-staff-leave-types.dto';
import { UpdateStaffLeaveTypeDto } from './dto/update-staff-leave-type.dto';
import { StaffLeaveTypeDto } from './dto/staff-leave-type.dto';
import {
  StaffLeaveTypeIdResponseDto,
  DeleteStaffLeaveTypeResponseDto,
  BulkAssignStaffLeaveTypeResponseDto,
  PaginatedStaffLeaveTypesResponseDto,
} from './dto/staff-leave-type-response.dto';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { staffLeaveTypes } from '../drizzle/schema/staff-leave-types.schema';
import { leaveTypes } from '../drizzle/schema/leave-types.schema';

import { staffFamilyDetails } from '../drizzle/schema/staff.schema';

import { staffEmergencyContacts } from '../drizzle/schema/staff.schema';
import { staffPhysicalInfo } from '../drizzle/schema/staff.schema';
import { media } from '../drizzle/schema/media.schema';
import { departments } from '../drizzle/schema/departments.schema';
import { designations } from '../drizzle/schema/designations.schema';
import { users } from '../drizzle/schema/users.schema';
import { alias } from 'drizzle-orm/pg-core';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  ActivityLogName,
  EmploymentStatus,
  EmploymentType,
  AddressType,
} from '../shared/types';
import { MediaService } from '../media/media.service';
import { MediaReferenceType } from '../drizzle/schema/media.schema';
import { UsersService } from '../users/users.service';
import { AddressService } from '../address/address.service';
import { LeaveBalancesService } from '../leave-balances/leave-balances.service';

interface PaginationMetaDto {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface StaffQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: EmploymentStatus;
  employmentType?: EmploymentType;
  departmentId?: string;
  designationId?: string;
  departmentName?: string;
  designationTitle?: string;
  sortBy?: 'displayName' | 'email' | 'createdAt' | 'dateOfJoining';
  sortOrder?: 'asc' | 'desc';
  from?: string;
  to?: string;
  filters?: string;
  joinOperator?: 'and' | 'or';
  sort?: string;
}

@Injectable()
export class StaffService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly usersService: UsersService,
    private readonly addressService: AddressService,
    private readonly leaveBalancesService: LeaveBalancesService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createStaffMemberDto: CreateStaffMemberDto,
    profileImageFile?: Express.Multer.File,
    documentFiles?: Express.Multer.File[],
  ): Promise<{ id: string }> {
    console.log('🚀 Starting staff creation process', {
      userId,
      businessId,
      email: createStaffMemberDto.email,
      displayName: createStaffMemberDto.displayName,
    });

    try {
      if (!businessId) {
        console.error('❌ No business ID provided');
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      console.log('✅ Business ID validated:', businessId);

      // Check for duplicate email within business
      console.log('🔍 Checking for duplicate email...');
      const existingStaff = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.email, createStaffMemberDto.email),
            isNull(staffMembers.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingStaff) {
        console.error('❌ Duplicate email found:', createStaffMemberDto.email);
        throw new ConflictException(
          'Staff member with this email already exists in this business',
        );
      }

      console.log('✅ Email is unique');

      // Check for duplicate display name within business
      console.log('🔍 Checking for duplicate display name...');
      const existingDisplayName = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.businessId, businessId),
            ilike(staffMembers.displayName, createStaffMemberDto.displayName),
            isNull(staffMembers.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingDisplayName) {
        console.error(
          '❌ Duplicate display name found:',
          createStaffMemberDto.displayName,
        );
        throw new ConflictException(
          'Staff member with this display name already exists in this business',
        );
      }

      console.log('✅ Display name is unique');

      // Check for duplicate employee ID if provided
      if (createStaffMemberDto.employeeId) {
        console.log('🔍 Checking for duplicate employee ID...');
        const existingEmployeeId = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.employeeId, createStaffMemberDto.employeeId),
              isNull(staffMembers.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingEmployeeId) {
          console.error(
            '❌ Duplicate employee ID found:',
            createStaffMemberDto.employeeId,
          );
          throw new ConflictException(
            'Staff member with this employee ID already exists in this business',
          );
        }

        console.log('✅ Employee ID is unique');
      }

      // Validate department and designation if provided
      if (createStaffMemberDto.departmentId) {
        console.log(
          '🔍 Validating department ID:',
          createStaffMemberDto.departmentId,
        );
        const departmentExists = await this.db
          .select()
          .from(departments)
          .where(
            and(
              eq(departments.id, createStaffMemberDto.departmentId),
              eq(departments.businessId, businessId),
              isNull(departments.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!departmentExists) {
          console.error(
            '❌ Department not found:',
            createStaffMemberDto.departmentId,
          );
          throw new BadRequestException('Department not found');
        }

        console.log('✅ Department validated');
      }

      if (createStaffMemberDto.designationId) {
        console.log(
          '🔍 Validating designation ID:',
          createStaffMemberDto.designationId,
        );
        const designationExists = await this.db
          .select()
          .from(designations)
          .where(
            and(
              eq(designations.id, createStaffMemberDto.designationId),
              eq(designations.businessId, businessId),
              isNull(designations.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!designationExists) {
          console.error(
            '❌ Designation not found:',
            createStaffMemberDto.designationId,
          );
          throw new BadRequestException('Designation not found');
        }

        console.log('✅ Designation validated');
      }

      console.log('🔄 Starting database transaction...');
      return await this.db.transaction(async (tx) => {
        console.log('📝 Inside transaction - processing staff creation');
        let profileImageId: string | undefined;
        let addressId: string | undefined;

        // Create address if provided
        if (createStaffMemberDto.address) {
          console.log('🏠 Creating address record...');
          const addressResult = await this.addressService.create(
            userId,
            {
              ...createStaffMemberDto.address,
              businessId,
              addressType:
                createStaffMemberDto.address.addressType || AddressType.HOME,
            },
            tx,
          );
          addressId = addressResult.id;
          console.log('✅ Address created with ID:', addressId);
        }

        // Create staff member
        console.log('👤 Creating staff member record...');
        const staffData = {
          businessId,
          departmentId: createStaffMemberDto.departmentId,
          designationId: createStaffMemberDto.designationId,
          firstName: createStaffMemberDto.firstName,
          lastName: createStaffMemberDto.lastName,
          displayName: createStaffMemberDto.displayName,
          email: createStaffMemberDto.email,
          phone: createStaffMemberDto.phone,
          isUser: createStaffMemberDto.isUser ?? false,
          employeeId: createStaffMemberDto.employeeId,
          dateOfBirth: createStaffMemberDto.dateOfBirth
            ? new Date(createStaffMemberDto.dateOfBirth)
            : undefined,
          dateOfJoining: createStaffMemberDto.dateOfJoining
            ? new Date(createStaffMemberDto.dateOfJoining)
            : undefined,
          employmentType:
            createStaffMemberDto.employmentType ?? EmploymentType.FULL_TIME,
          addressId,
          status: createStaffMemberDto.status ?? EmploymentStatus.ACTIVE,
          createdBy: userId,
        };

        console.log(
          '📊 Staff data to insert:',
          JSON.stringify(staffData, null, 2),
        );

        const [staffMember] = await tx
          .insert(staffMembers)
          .values(staffData)
          .returning();

        // Handle profile image upload with reference ID
        if (profileImageFile) {
          console.log('📷 Uploading profile image...');
          try {
            const mediaRecord =
              await this.mediaService.uploadMediaWithReference(
                profileImageFile,
                MediaReferenceType.STAFF,
                businessId,
                userId,
                staffMember.id, // referenceId as staff member ID
              );
            profileImageId = mediaRecord.id;

            // Update staff member with profile image ID
            await tx
              .update(staffMembers)
              .set({ profileImageId })
              .where(eq(staffMembers.id, staffMember.id));

            console.log('✅ Profile image uploaded:', profileImageId);
          } catch (error) {
            console.error('❌ Profile image upload failed:', error.message);
            throw new BadRequestException(
              `Profile image upload failed: ${error.message}`,
            );
          }
        }

        console.log('✅ Staff member created with ID:', staffMember.id);

        if (createStaffMemberDto.familyDetails) {
          const familyDto = createStaffMemberDto.familyDetails;
          await tx.insert(staffFamilyDetails).values({
            businessId,
            staffMemberId: staffMember.id,
            maritalStatus: familyDto.maritalStatus,
            numberOfChildren: familyDto.numberOfChildren ?? 0,
            isSpouseWorking: familyDto.isSpouseWorking,
            spouseFirstName: familyDto.spouseFirstName || familyDto.spouseName,
            spouseMiddleName: familyDto.spouseMiddleName,
            spouseLastName: familyDto.spouseLastName,
            spouseBirthDate: familyDto.spouseBirthDate,
            spouseGender: familyDto.spouseGender,
            spouseEmail: familyDto.spouseEmail,
            spousePhone: familyDto.spousePhone,
            notes:
              [
                familyDto.notes,
                familyDto.spouseOccupation
                  ? `Spouse Occupation: ${familyDto.spouseOccupation}`
                  : null,
                familyDto.fatherName
                  ? `Father's Name: ${familyDto.fatherName}`
                  : null,
                familyDto.motherName
                  ? `Mother's Name: ${familyDto.motherName}`
                  : null,
              ]
                .filter(Boolean)
                .join('; ') || undefined,
            createdBy: userId,
          });
        }

        // Handle document uploads
        if (documentFiles && documentFiles.length > 0) {
          // Use bulk upload with staff member ID as reference
          await this.mediaService.uploadMultipleMediaWithReference(
            documentFiles,
            'staff/documents',
            businessId,
            userId,
            staffMember.id, // referenceId as staff member ID
          );
        }

        // Create emergency contacts
        if (createStaffMemberDto.emergencyContacts) {
          for (const contactDto of createStaffMemberDto.emergencyContacts) {
            await tx.insert(staffEmergencyContacts).values({
              businessId,
              staffMemberId: staffMember.id,
              firstName: contactDto.firstName,
              middleName: contactDto.middleName,
              lastName: contactDto.lastName,
              relationship: contactDto.relationship,
              mobilePhone: contactDto.mobilePhone,
              housePhone: contactDto.housePhone,
              officePhone: contactDto.officePhone,
              addressId: contactDto.addressId,
              priority: contactDto.priority ?? 1,
              isActive: contactDto.isActive ?? true,
              notes: contactDto.notes,
              createdBy: userId,
            });
          }
        }

        // Create physical info
        if (createStaffMemberDto.physicalInfo) {
          const physicalDto = createStaffMemberDto.physicalInfo;
          await tx.insert(staffPhysicalInfo).values({
            businessId,
            staffMemberId: staffMember.id,
            heightCm: physicalDto.heightCm,
            weightKg: physicalDto.weightKg,
            bloodType: physicalDto.bloodType,
            visionLeft: physicalDto.visionLeft,
            visionRight: physicalDto.visionRight,
            hearingLeft: physicalDto.hearingLeft,
            hearingRight: physicalDto.hearingRight,
            handLeft: physicalDto.handLeft,
            handRight: physicalDto.handRight,
            legLeft: physicalDto.legLeft,
            legRight: physicalDto.legRight,
            createdBy: userId,
          });
        }

        // Log activity
        console.log('📝 Logging activity...');
        try {
          await this.activityLogService.log(
            ActivityLogName.CREATE,
            `Staff member "${createStaffMemberDto.displayName}" was created`,
            { id: staffMember.id, type: 'staff_member' },
            { id: userId, type: 'user' },
            { staffMemberId: staffMember.id, businessId },
          );
          console.log('✅ Activity logged successfully');
        } catch (error) {
          console.error('❌ Activity logging failed:', error.message);
          // Don't throw here as the staff member was created successfully
        }

        console.log('🔍 Fetching created staff member details...');
        console.log('✅ Staff member creation completed successfully');
        return { id: staffMember.id };
      });
    } catch (error) {
      console.error('❌ Staff creation failed:', {
        error: error.message,
        stack: error.stack,
        userId,
        businessId,
        email: createStaffMemberDto.email,
      });

      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create staff member: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    queryParams: StaffQueryParams = {},
  ): Promise<PaginatedStaffResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const {
      page = 1,
      limit = 10,
      search,
      status,
      employmentType,
      departmentId,
      designationId,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      from,
      to,
    } = queryParams;

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(staffMembers.businessId, businessId),
      isNull(staffMembers.deletedAt),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(staffMembers.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(staffMembers.createdAt, toDate));
      }
    }

    if (search) {
      whereConditions.push(
        or(
          ilike(staffMembers.displayName, `%${search}%`),
          ilike(staffMembers.email, `%${search}%`),
          ilike(staffMembers.firstName, `%${search}%`),
          ilike(staffMembers.lastName, `%${search}%`),
          ilike(staffMembers.employeeId, `%${search}%`),
        ),
      );
    }

    if (status) {
      whereConditions.push(eq(staffMembers.status, status));
    }

    if (employmentType) {
      whereConditions.push(eq(staffMembers.employmentType, employmentType));
    }

    if (departmentId) {
      whereConditions.push(eq(staffMembers.departmentId, departmentId));
    }

    if (designationId) {
      whereConditions.push(eq(staffMembers.designationId, designationId));
    }

    // Build sort order
    const orderBy = sortOrder === 'asc' ? asc : desc;
    let sortColumn: any;
    switch (sortBy) {
      case 'displayName':
        sortColumn = staffMembers.displayName;
        break;
      case 'email':
        sortColumn = staffMembers.email;
        break;
      case 'dateOfJoining':
        sortColumn = staffMembers.dateOfJoining;
        break;
      default:
        sortColumn = staffMembers.createdAt;
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(staffMembers)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Create aliases for user tables to handle audit fields
    const createdByUser = alias(users, 'created_by_user');
    const updatedByUser = alias(users, 'updated_by_user');

    // Get staff members with related data - only fetch required fields for list DTO
    const staffList = await this.db
      .select({
        id: staffMembers.id,
        businessId: staffMembers.businessId,
        firstName: staffMembers.firstName,
        lastName: staffMembers.lastName,
        displayName: staffMembers.displayName,
        email: staffMembers.email,
        phone: staffMembers.phone,
        employeeId: staffMembers.employeeId,
        employmentType: staffMembers.employmentType,
        status: staffMembers.status,
        profileImageId: staffMembers.profileImageId,
        departmentName: departments.name,
        designationTitle: designations.name,
        profileImageUrl: media.publicUrl,
        // Audit fields
        createdBy: staffMembers.createdBy,
        updatedBy: staffMembers.updatedBy,
        createdAt: staffMembers.createdAt,
        updatedAt: staffMembers.updatedAt,
        // Creator user info
        createdByFirstName: createdByUser.firstName,
        createdByLastName: createdByUser.lastName,
        createdByAvatar: createdByUser.avatar,
        // Updater user info
        updatedByFirstName: updatedByUser.firstName,
        updatedByLastName: updatedByUser.lastName,
        updatedByAvatar: updatedByUser.avatar,
      })
      .from(staffMembers)
      .leftJoin(departments, eq(staffMembers.departmentId, departments.id))
      .leftJoin(designations, eq(staffMembers.designationId, designations.id))
      .leftJoin(media, eq(staffMembers.profileImageId, media.id))
      .leftJoin(
        createdByUser as any,
        eq(staffMembers.createdBy, createdByUser.id),
      )
      .leftJoin(
        updatedByUser as any,
        eq(staffMembers.updatedBy, updatedByUser.id),
      )
      .where(and(...whereConditions))
      .orderBy(orderBy(sortColumn), asc(staffMembers.id))
      .limit(limit)
      .offset(offset);

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed staff members',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    const data: StaffMemberListDto[] = await Promise.all(
      staffList.map(async (staff) => {
        let profileImageUrl: string | undefined;

        // Generate signed URL for profile image if it exists
        if (staff.profileImageId) {
          try {
            profileImageUrl = await this.mediaService.generateSignedUrlForMedia(
              staff.profileImageId,
              businessId,
              'staff/profile-images',
              60, // expiration in minutes
            );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for staff ${staff.id} profile image:`,
              error.message,
            );
            profileImageUrl = staff.profileImageUrl || undefined;
          }
        }

        return {
          id: staff.id,
          businessId: staff.businessId,
          firstName: staff.firstName,
          lastName: staff.lastName,
          displayName: staff.displayName,
          email: staff.email,
          phone: staff.phone,
          employeeId: staff.employeeId,
          departmentName: staff.departmentName,
          designationTitle: staff.designationTitle,
          employmentType: staff.employmentType,
          status: staff.status,
          profileImageUrl,
          // Audit fields
          createdBy: {
            id: staff.createdBy,
            firstName: staff.createdByFirstName || '',
            lastName: staff.createdByLastName || '',
            avatar: staff.createdByAvatar,
          },
          updatedBy: staff.updatedBy
            ? {
                id: staff.updatedBy,
                firstName: staff.updatedByFirstName || '',
                lastName: staff.updatedByLastName || '',
                avatar: staff.updatedByAvatar,
              }
            : undefined,
          createdAt: staff.createdAt,
          updatedAt: staff.updatedAt,
        };
      }),
    );

    const meta: PaginationMetaDto = {
      page,
      limit,
      total,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };

    return { data, meta };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    queryParams: StaffQueryParams = {},
  ): Promise<PaginatedStaffResponseDto> {
    console.log('🔍 Starting findAllOptimized:', {
      userId,
      businessId,
      queryParams,
    });

    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate database connection
      if (!this.db) {
        console.error('❌ Database connection is null or undefined');
        throw new BadRequestException('Database connection error');
      }

      const {
        page = 1,
        limit = 10,
        search,
        status,
        employmentType,
        departmentId,
        designationId,
        departmentName,
        designationTitle,
        from,
        to,
        filters,
        joinOperator = 'and',
        sort,
      } = queryParams;

      const offset = (page - 1) * limit;

      // Build base where conditions
      const whereConditions = [
        eq(staffMembers.businessId, businessId),
        isNull(staffMembers.deletedAt),
      ];

      // Add date range filtering if provided
      if (from) {
        const fromDate = new Date(from);
        if (!isNaN(fromDate.getTime())) {
          whereConditions.push(gte(staffMembers.createdAt, fromDate));
        }
      }

      if (to) {
        const toDate = new Date(to);
        if (!isNaN(toDate.getTime())) {
          toDate.setHours(23, 59, 59, 999);
          whereConditions.push(lte(staffMembers.createdAt, toDate));
        }
      }

      // Add search filtering if provided
      if (search) {
        whereConditions.push(
          or(
            ilike(staffMembers.displayName, `%${search}%`),
            ilike(staffMembers.email, `%${search}%`),
            ilike(staffMembers.firstName, `%${search}%`),
            ilike(staffMembers.lastName, `%${search}%`),
            ilike(staffMembers.employeeId, `%${search}%`),
          ),
        );
      }

      // Add status filtering if provided
      if (status) {
        whereConditions.push(eq(staffMembers.status, status));
      }

      // Add employment type filtering if provided
      if (employmentType) {
        whereConditions.push(eq(staffMembers.employmentType, employmentType));
      }

      // Add department filtering if provided
      if (departmentId) {
        whereConditions.push(eq(staffMembers.departmentId, departmentId));
      }

      // Add designation filtering if provided
      if (designationId) {
        whereConditions.push(eq(staffMembers.designationId, designationId));
      }

      // Add department name filtering if provided
      if (departmentName) {
        whereConditions.push(eq(departments.name, departmentName));
      }

      // Add designation title filtering if provided
      if (designationTitle) {
        whereConditions.push(eq(designations.name, designationTitle));
      }

      // Add advanced filters if provided
      if (filters) {
        try {
          const parsedFilters = JSON.parse(filters);
          const filterConditions = [];

          for (const filter of parsedFilters) {
            const { id: fieldId, value, operator } = filter;

            if (fieldId === 'displayName') {
              switch (operator) {
                case 'contains':
                case 'iLike':
                  filterConditions.push(
                    ilike(staffMembers.displayName, `%${value}%`),
                  );
                  break;
                case 'not_contains':
                case 'notILike':
                  filterConditions.push(
                    sql`NOT ${ilike(staffMembers.displayName, `%${value}%`)}`,
                  );
                  break;
                case 'equals':
                case 'eq':
                  filterConditions.push(eq(staffMembers.displayName, value));
                  break;
                case 'not_equals':
                case 'ne':
                  filterConditions.push(
                    sql`${staffMembers.displayName} != ${value}`,
                  );
                  break;
                case 'starts_with':
                  filterConditions.push(
                    ilike(staffMembers.displayName, `${value}%`),
                  );
                  break;
                case 'ends_with':
                  filterConditions.push(
                    ilike(staffMembers.displayName, `%${value}`),
                  );
                  break;
                case 'is_empty':
                case 'isEmpty':
                  filterConditions.push(
                    sql`${staffMembers.displayName} IS NULL OR ${staffMembers.displayName} = ''`,
                  );
                  break;
                case 'is_not_empty':
                case 'isNotEmpty':
                  filterConditions.push(
                    sql`${staffMembers.displayName} IS NOT NULL AND ${staffMembers.displayName} != ''`,
                  );
                  break;
              }
            } else if (fieldId === 'email') {
              switch (operator) {
                case 'contains':
                case 'iLike':
                  filterConditions.push(
                    ilike(staffMembers.email, `%${value}%`),
                  );
                  break;
                case 'not_contains':
                case 'notILike':
                  filterConditions.push(
                    sql`NOT ${ilike(staffMembers.email, `%${value}%`)}`,
                  );
                  break;
                case 'equals':
                case 'eq':
                  filterConditions.push(eq(staffMembers.email, value));
                  break;
                case 'not_equals':
                case 'ne':
                  filterConditions.push(sql`${staffMembers.email} != ${value}`);
                  break;
                case 'starts_with':
                  filterConditions.push(ilike(staffMembers.email, `${value}%`));
                  break;
                case 'ends_with':
                  filterConditions.push(ilike(staffMembers.email, `%${value}`));
                  break;
                case 'is_empty':
                case 'isEmpty':
                  filterConditions.push(
                    sql`${staffMembers.email} IS NULL OR ${staffMembers.email} = ''`,
                  );
                  break;
                case 'is_not_empty':
                case 'isNotEmpty':
                  filterConditions.push(
                    sql`${staffMembers.email} IS NOT NULL AND ${staffMembers.email} != ''`,
                  );
                  break;
              }
            } else if (fieldId === 'status') {
              switch (operator) {
                case 'equals':
                case 'eq':
                case 'in':
                  if (Array.isArray(value)) {
                    filterConditions.push(
                      inArray(staffMembers.status, value as EmploymentStatus[]),
                    );
                  } else {
                    filterConditions.push(eq(staffMembers.status, value));
                  }
                  break;
                case 'not_equals':
                case 'ne':
                case 'not_in':
                  if (Array.isArray(value)) {
                    filterConditions.push(
                      sql`${staffMembers.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                    );
                  } else {
                    filterConditions.push(
                      sql`${staffMembers.status} != ${value}`,
                    );
                  }
                  break;
              }
            } else if (fieldId === 'employmentType') {
              switch (operator) {
                case 'equals':
                case 'eq':
                case 'in':
                  if (Array.isArray(value)) {
                    filterConditions.push(
                      inArray(
                        staffMembers.employmentType,
                        value as EmploymentType[],
                      ),
                    );
                  } else {
                    filterConditions.push(
                      eq(staffMembers.employmentType, value),
                    );
                  }
                  break;
                case 'not_equals':
                case 'ne':
                case 'not_in':
                  if (Array.isArray(value)) {
                    filterConditions.push(
                      sql`${staffMembers.employmentType} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                    );
                  } else {
                    filterConditions.push(
                      sql`${staffMembers.employmentType} != ${value}`,
                    );
                  }
                  break;
              }
            } else if (fieldId === 'departmentName') {
              switch (operator) {
                case 'equals':
                case 'eq':
                case 'in':
                  if (Array.isArray(value)) {
                    filterConditions.push(
                      inArray(departments.name, value as string[]),
                    );
                  } else {
                    filterConditions.push(eq(departments.name, value));
                  }
                  break;
                case 'not_equals':
                case 'ne':
                case 'not_in':
                  if (Array.isArray(value)) {
                    filterConditions.push(
                      sql`${departments.name} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                    );
                  } else {
                    filterConditions.push(sql`${departments.name} != ${value}`);
                  }
                  break;
                case 'contains':
                case 'iLike':
                  filterConditions.push(ilike(departments.name, `%${value}%`));
                  break;
                case 'not_contains':
                case 'notILike':
                  filterConditions.push(
                    sql`NOT ${ilike(departments.name, `%${value}%`)}`,
                  );
                  break;
                case 'starts_with':
                  filterConditions.push(ilike(departments.name, `${value}%`));
                  break;
                case 'ends_with':
                  filterConditions.push(ilike(departments.name, `%${value}`));
                  break;
                case 'is_empty':
                case 'isEmpty':
                  filterConditions.push(
                    sql`${departments.name} IS NULL OR ${departments.name} = ''`,
                  );
                  break;
                case 'is_not_empty':
                case 'isNotEmpty':
                  filterConditions.push(
                    sql`${departments.name} IS NOT NULL AND ${departments.name} != ''`,
                  );
                  break;
              }
            } else if (fieldId === 'designationTitle') {
              switch (operator) {
                case 'equals':
                case 'eq':
                case 'in':
                  if (Array.isArray(value)) {
                    filterConditions.push(
                      inArray(designations.name, value as string[]),
                    );
                  } else {
                    filterConditions.push(eq(designations.name, value));
                  }
                  break;
                case 'not_equals':
                case 'ne':
                case 'not_in':
                  if (Array.isArray(value)) {
                    filterConditions.push(
                      sql`${designations.name} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                    );
                  } else {
                    filterConditions.push(
                      sql`${designations.name} != ${value}`,
                    );
                  }
                  break;
                case 'contains':
                case 'iLike':
                  filterConditions.push(ilike(designations.name, `%${value}%`));
                  break;
                case 'not_contains':
                case 'notILike':
                  filterConditions.push(
                    sql`NOT ${ilike(designations.name, `%${value}%`)}`,
                  );
                  break;
                case 'starts_with':
                  filterConditions.push(ilike(designations.name, `${value}%`));
                  break;
                case 'ends_with':
                  filterConditions.push(ilike(designations.name, `%${value}`));
                  break;
                case 'is_empty':
                case 'isEmpty':
                  filterConditions.push(
                    sql`${designations.name} IS NULL OR ${designations.name} = ''`,
                  );
                  break;
                case 'is_not_empty':
                case 'isNotEmpty':
                  filterConditions.push(
                    sql`${designations.name} IS NOT NULL AND ${designations.name} != ''`,
                  );
                  break;
              }
            }
          }

          if (filterConditions.length > 0) {
            if (joinOperator === 'or') {
              whereConditions.push(or(...filterConditions));
            } else {
              whereConditions.push(and(...filterConditions));
            }
          }
        } catch {
          // Invalid JSON, ignore filters
        }
      }

      // Build optimized sort conditions - default to latest created first
      let orderBy = [desc(staffMembers.createdAt), asc(staffMembers.id)];

      if (sort) {
        try {
          const parsedSort = JSON.parse(sort);
          if (parsedSort.length > 0) {
            const sortField = parsedSort[0];
            const isDesc = sortField.desc === true;

            switch (sortField.id) {
              case 'displayName':
                orderBy = [
                  isDesc
                    ? desc(staffMembers.displayName)
                    : asc(staffMembers.displayName),
                  asc(staffMembers.id),
                ];
                break;
              case 'email':
                orderBy = [
                  isDesc ? desc(staffMembers.email) : asc(staffMembers.email),
                  asc(staffMembers.id),
                ];
                break;
              case 'createdAt':
                orderBy = [
                  isDesc
                    ? desc(staffMembers.createdAt)
                    : asc(staffMembers.createdAt),
                  asc(staffMembers.id),
                ];
                break;
              case 'dateOfJoining':
                orderBy = [
                  isDesc
                    ? desc(staffMembers.dateOfJoining)
                    : asc(staffMembers.dateOfJoining),
                  asc(staffMembers.id),
                ];
                break;
            }
          }
        } catch {
          // Invalid JSON, use default sort
        }
      }

      // Execute optimized query - only fetch required fields for list DTO
      const result = await this.db
        .select({
          id: staffMembers.id,
          businessId: staffMembers.businessId,
          displayName: staffMembers.displayName,
          email: staffMembers.email,
          phone: staffMembers.phone,
          employeeId: staffMembers.employeeId,
          employmentType: staffMembers.employmentType,
          status: staffMembers.status,
          profileImageId: staffMembers.profileImageId,
          profileImagePublicUrl: media.publicUrl,
          departmentName: departments.name,
          designationTitle: designations.name,
          createdBy: staffMembers.createdBy,
          updatedBy: staffMembers.updatedBy,
          createdAt: staffMembers.createdAt,
          updatedAt: staffMembers.updatedAt,
        })
        .from(staffMembers)
        .leftJoin(departments, eq(staffMembers.departmentId, departments.id))
        .leftJoin(designations, eq(staffMembers.designationId, designations.id))
        .leftJoin(media, eq(staffMembers.profileImageId, media.id))
        .where(and(...whereConditions))
        .orderBy(...orderBy)
        .limit(limit)
        .offset(offset);

      // Ensure result is an array
      if (!Array.isArray(result)) {
        console.error('❌ Query result is not an array:', result);
        throw new BadRequestException('Invalid query result format');
      }

      // Get total count for pagination
      const totalResult = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(staffMembers)
        .where(and(...whereConditions));

      // Safely get total count
      if (!Array.isArray(totalResult) || totalResult.length === 0) {
        console.error(
          '❌ Total count query returned invalid result:',
          totalResult,
        );
        throw new BadRequestException('Failed to get total count');
      }

      const total = Number(totalResult[0]?.count || 0);
      const totalPages = Math.ceil(total / limit);

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.VIEW,
        'Viewed staff members (optimized)',
        { id: businessId, type: 'business' },
        { id: userId, type: 'user' },
      );

      // Generate signed URLs and build final data
      let data: StaffMemberListDto[] = [];

      try {
        data = await Promise.all(
          result.map(async (staff) => {
            // Ensure staff object is valid
            if (!staff || !staff.id) {
              console.warn('❌ Invalid staff object found:', staff);
              return null;
            }

            let profileImageUrl: string | undefined;

            if (staff.profileImageId) {
              try {
                profileImageUrl =
                  await this.mediaService.generateSignedUrlForMedia(
                    staff.profileImageId,
                    businessId,
                    'staff/profile-images',
                    60, // expiration in minutes
                  );
              } catch (error) {
                console.warn(
                  `Failed to generate signed URL for staff ${staff.id} profile image:`,
                  error.message,
                );
                profileImageUrl = staff.profileImagePublicUrl || undefined;
              }
            }

            // Get audit user information
            const [createdByUser, updatedByUser] = await Promise.all([
              this.getAuditUserInfo(staff.createdBy),
              staff.updatedBy
                ? this.getAuditUserInfo(staff.updatedBy)
                : undefined,
            ]);

            return {
              id: staff.id,
              businessId: staff.businessId,
              displayName: staff.displayName || '',
              email: staff.email || '',
              phone: staff.phone,
              employeeId: staff.employeeId,
              departmentName: staff.departmentName,
              designationTitle: staff.designationTitle,
              employmentType: staff.employmentType,
              status: staff.status,
              profileImageUrl,
              createdBy: createdByUser,
              updatedBy: updatedByUser,
              createdAt: staff.createdAt,
              updatedAt: staff.updatedAt,
            };
          }),
        );

        // Filter out any null results
        data = data.filter((item) => item !== null);
      } catch (error) {
        console.error('❌ Error building staff data:', error);
        throw new BadRequestException('Failed to process staff data');
      }

      const meta: PaginationMetaDto = {
        page,
        limit,
        total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };

      console.log('✅ findAllOptimized completed successfully:', {
        dataCount: data.length,
        total,
        page,
        totalPages,
      });

      return { data, meta };
    } catch (error) {
      console.error('❌ findAllOptimized failed:', {
        error: error.message,
        stack: error.stack,
        userId,
        businessId,
        queryParams,
      });

      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to fetch staff members: ${error.message}`,
      );
    }
  }

  async checkDisplayNameAvailability(
    businessId: string | null,
    displayName: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a staff member with the same display name already exists for this business
    // Using ilike for case-insensitive comparison
    const existingStaffMember = await this.db
      .select()
      .from(staffMembers)
      .where(
        and(
          eq(staffMembers.businessId, businessId),
          ilike(staffMembers.displayName, displayName),
          isNull(staffMembers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingStaffMember };
  }

  async checkEmailAvailability(
    businessId: string | null,
    email: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a staff member with the same email already exists for this business
    const existingStaffMember = await this.db
      .select()
      .from(staffMembers)
      .where(
        and(
          eq(staffMembers.businessId, businessId),
          eq(staffMembers.email, email),
          isNull(staffMembers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingStaffMember };
  }

  async checkEmployeeIdAvailability(
    businessId: string | null,
    employeeId: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a staff member with the same employee ID already exists for this business
    const existingStaffMember = await this.db
      .select()
      .from(staffMembers)
      .where(
        and(
          eq(staffMembers.businessId, businessId),
          eq(staffMembers.employeeId, employeeId),
          isNull(staffMembers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingStaffMember };
  }

  async findOne(userId: string, id: string): Promise<StaffMemberDto> {
    console.log('🔍 Finding staff member:', { userId, staffId: id });

    // Get the staff member
    const staffMemberQuery = await this.db
      .select()
      .from(staffMembers)
      .where(and(eq(staffMembers.id, id), isNull(staffMembers.deletedAt)))
      .then((results) => results[0]);

    if (!staffMemberQuery) {
      console.error('❌ Staff member not found in database:', id);
      throw new NotFoundException(`Staff member with ID ${id} not found`);
    }

    console.log('✅ Staff member found:', {
      id: staffMemberQuery.id,
      email: staffMemberQuery.email,
      businessId: staffMemberQuery.businessId,
    });

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== staffMemberQuery.businessId
    ) {
      console.error('❌ Access denied - business mismatch:', {
        userBusinessId: user?.activeBusinessId,
        staffBusinessId: staffMemberQuery.businessId,
      });
      throw new UnauthorizedException('Access denied to this staff member');
    }

    // Log the activity
    try {
      await this.activityLogService.log(
        ActivityLogName.VIEW,
        `Viewed staff member "${staffMemberQuery.displayName}"`,
        { id: id, type: 'staff_member' },
        { id: userId, type: 'user' },
      );
    } catch (error) {
      console.warn('⚠️ Activity logging failed in findOne:', error.message);
    }

    console.log('🔄 Mapping staff member to DTO...');
    const result = await this.mapToStaffMemberDto(
      staffMemberQuery,
      user.activeBusinessId,
    );
    console.log('✅ Staff member DTO mapped successfully');
    return result;
  }

  private async mapToStaffMemberDto(
    staffMember: typeof staffMembers.$inferSelect,
    businessId: string,
  ): Promise<StaffMemberDto> {
    const staffId = staffMember.id;

    // Get related data in parallel for better performance
    const [
      staffWithDetails,
      familyDetails,
      staffDocuments,
      emergencyContacts,
      physicalInfo,
    ] = await Promise.all([
      this.db
        .select({
          id: staffMembers.id,
          businessId: staffMembers.businessId,
          userId: staffMembers.userId,
          departmentId: staffMembers.departmentId,
          designationId: staffMembers.designationId,
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
          displayName: staffMembers.displayName,
          email: staffMembers.email,
          phone: staffMembers.phone,
          isUser: staffMembers.isUser,
          employeeId: staffMembers.employeeId,
          dateOfBirth: staffMembers.dateOfBirth,
          dateOfJoining: staffMembers.dateOfJoining,
          employmentType: staffMembers.employmentType,
          addressId: staffMembers.addressId,
          profileImageId: staffMembers.profileImageId,
          status: staffMembers.status,
          createdBy: staffMembers.createdBy,
          updatedBy: staffMembers.updatedBy,
          createdAt: staffMembers.createdAt,
          updatedAt: staffMembers.updatedAt,
          departmentName: departments.name,
          departmentIdFromJoin: departments.id,
          designationTitle: designations.name,
          designationIdFromJoin: designations.id,
          profileImageUrl: media.publicUrl,
        })
        .from(staffMembers)
        .leftJoin(departments, eq(staffMembers.departmentId, departments.id))
        .leftJoin(designations, eq(staffMembers.designationId, designations.id))
        .leftJoin(media, eq(staffMembers.profileImageId, media.id))
        .leftJoin(users, eq(staffMembers.createdBy, users.id))
        .where(
          and(
            eq(staffMembers.id, staffId),
            eq(staffMembers.businessId, businessId),
            isNull(staffMembers.deletedAt),
          ),
        )
        .then((results) => results[0]),

      this.db
        .select()
        .from(staffFamilyDetails)
        .where(
          and(
            eq(staffFamilyDetails.staffMemberId, staffId),
            isNull(staffFamilyDetails.deletedAt),
          ),
        )
        .then((results) => results[0]),

      // Get staff documents from media service using referenceId
      this.mediaService.findByReferenceId(staffId, businessId),

      this.db
        .select()
        .from(staffEmergencyContacts)
        .where(
          and(
            eq(staffEmergencyContacts.staffMemberId, staffId),
            isNull(staffEmergencyContacts.deletedAt),
          ),
        )
        .orderBy(asc(staffEmergencyContacts.priority)),

      this.db
        .select({
          id: staffPhysicalInfo.id,
          businessId: staffPhysicalInfo.businessId,
          staffMemberId: staffPhysicalInfo.staffMemberId,
          heightCm: staffPhysicalInfo.heightCm,
          weightKg: staffPhysicalInfo.weightKg,
          bloodType: staffPhysicalInfo.bloodType,
          visionLeft: staffPhysicalInfo.visionLeft,
          visionRight: staffPhysicalInfo.visionRight,
          hearingLeft: staffPhysicalInfo.hearingLeft,
          hearingRight: staffPhysicalInfo.hearingRight,
          handLeft: staffPhysicalInfo.handLeft,
          handRight: staffPhysicalInfo.handRight,
          legLeft: staffPhysicalInfo.legLeft,
          legRight: staffPhysicalInfo.legRight,
          createdBy: staffPhysicalInfo.createdBy,
          updatedBy: staffPhysicalInfo.updatedBy,
          createdAt: staffPhysicalInfo.createdAt,
          updatedAt: staffPhysicalInfo.updatedAt,
          createdByName: users.displayName,
        })
        .from(staffPhysicalInfo)
        .leftJoin(users, eq(staffPhysicalInfo.createdBy, users.id))
        .where(
          and(
            eq(staffPhysicalInfo.staffMemberId, staffId),
            isNull(staffPhysicalInfo.deletedAt),
          ),
        )
        .then((results) => results[0]),
    ]);

    if (!staffWithDetails) {
      throw new NotFoundException('Staff member not found');
    }

    // Generate signed URL for profile image if it exists
    let profileImageUrl: string | undefined;
    if (staffWithDetails.profileImageId) {
      try {
        profileImageUrl = await this.mediaService.generateSignedUrlForMedia(
          staffWithDetails.profileImageId,
          businessId,
          'staff/profile-images',
          60, // expiration in minutes
        );
      } catch (error) {
        console.warn(
          `Failed to generate signed URL for staff ${staffId} profile image:`,
          error.message,
        );
        profileImageUrl = staffWithDetails.profileImageUrl || undefined;
      }
    }

    // Generate signed URLs for documents from media service
    const documentsWithUrls = await Promise.all(
      (staffDocuments || []).map(async (doc) => {
        let documentUrl: string | undefined;

        // Generate signed URL for document if it exists
        if (doc.id) {
          try {
            documentUrl = await this.mediaService.generateSignedUrlForMedia(
              doc.id,
              businessId,
              'staff/documents',
              60, // expiration in minutes
            );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for document ${doc.id}:`,
              error.message,
            );
            documentUrl = doc.publicUrl || undefined;
          }
        }

        return {
          id: doc.id,
          businessId: doc.businessId,
          staffMemberId: staffId, // Use the staffId from context
          mediaId: doc.id,
          documentTitle: doc.originalName, // Use original filename as title
          documentUrl,
          originalFileName: doc.originalName,
          fileSize: doc.size,
          mimeType: doc.mimeType,
        };
      }),
    );

    return {
      id: staffWithDetails.id,
      businessId: staffWithDetails.businessId,
      userId: staffWithDetails.userId,
      departmentId: staffWithDetails.departmentId,
      departmentName: staffWithDetails.departmentName,
      department:
        staffWithDetails.departmentIdFromJoin && staffWithDetails.departmentName
          ? {
              id: staffWithDetails.departmentIdFromJoin,
              name: staffWithDetails.departmentName,
            }
          : undefined,
      designationId: staffWithDetails.designationId,
      designationTitle: staffWithDetails.designationTitle,
      designation:
        staffWithDetails.designationIdFromJoin &&
        staffWithDetails.designationTitle
          ? {
              id: staffWithDetails.designationIdFromJoin,
              name: staffWithDetails.designationTitle,
            }
          : undefined,
      firstName: staffWithDetails.firstName,
      lastName: staffWithDetails.lastName,
      displayName: staffWithDetails.displayName,
      email: staffWithDetails.email,
      phone: staffWithDetails.phone,
      isUser: staffWithDetails.isUser,
      employeeId: staffWithDetails.employeeId,
      dateOfBirth: staffWithDetails.dateOfBirth,
      dateOfJoining: staffWithDetails.dateOfJoining,
      employmentType: staffWithDetails.employmentType,
      addressId: staffWithDetails.addressId,
      profileImageId: staffWithDetails.profileImageId,
      profileImageUrl,
      status: staffWithDetails.status,

      // Add audit fields
      createdBy: await this.getAuditUserInfo(staffWithDetails.createdBy),
      updatedBy: staffWithDetails.updatedBy
        ? await this.getAuditUserInfo(staffWithDetails.updatedBy)
        : undefined,
      createdAt: staffWithDetails.createdAt,
      updatedAt: staffWithDetails.updatedAt,

      familyDetails: familyDetails || undefined,
      documents: documentsWithUrls || [],
      emergencyContacts: emergencyContacts || [],
      physicalInfo: physicalInfo
        ? {
            id: physicalInfo.id,
            businessId: physicalInfo.businessId,
            staffMemberId: physicalInfo.staffMemberId,
            heightCm: physicalInfo.heightCm,
            weightKg: physicalInfo.weightKg,
            bloodType: physicalInfo.bloodType,
            visionLeft: physicalInfo.visionLeft,
            visionRight: physicalInfo.visionRight,
            hearingLeft: physicalInfo.hearingLeft,
            hearingRight: physicalInfo.hearingRight,
            handLeft: physicalInfo.handLeft,
            handRight: physicalInfo.handRight,
            legLeft: physicalInfo.legLeft,
            legRight: physicalInfo.legRight,
            createdBy: physicalInfo.createdByName,
            updatedBy: undefined, // Will be populated if needed
            createdAt: physicalInfo.createdAt,
            updatedAt: physicalInfo.updatedAt,
          }
        : undefined,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateStaffMemberDto: UpdateStaffMemberDto,
    profileImageFile?: Express.Multer.File,
    documentFiles?: Express.Multer.File[],
  ): Promise<StaffMemberDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if staff member exists
      const existingStaff = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, id),
            eq(staffMembers.businessId, businessId),
            isNull(staffMembers.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingStaff) {
        throw new NotFoundException('Staff member not found');
      }

      // Check for duplicate email if email is being updated
      if (
        updateStaffMemberDto.email &&
        updateStaffMemberDto.email !== existingStaff.email
      ) {
        const duplicateEmail = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.email, updateStaffMemberDto.email),
              sql`${staffMembers.id} != ${id}`,
              isNull(staffMembers.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (duplicateEmail) {
          throw new ConflictException(
            'Staff member with this email already exists in this business',
          );
        }
      }

      // Check for duplicate display name if display name is being updated
      if (
        updateStaffMemberDto.displayName &&
        updateStaffMemberDto.displayName !== existingStaff.displayName
      ) {
        const duplicateDisplayName = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.businessId, businessId),
              ilike(staffMembers.displayName, updateStaffMemberDto.displayName),
              sql`${staffMembers.id} != ${id}`,
              isNull(staffMembers.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (duplicateDisplayName) {
          throw new ConflictException(
            'Staff member with this display name already exists in this business',
          );
        }
      }

      // Check for duplicate employee ID if employee ID is being updated
      if (
        updateStaffMemberDto.employeeId &&
        updateStaffMemberDto.employeeId !== existingStaff.employeeId
      ) {
        const duplicateEmployeeId = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.employeeId, updateStaffMemberDto.employeeId),
              sql`${staffMembers.id} != ${id}`,
              isNull(staffMembers.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (duplicateEmployeeId) {
          throw new ConflictException(
            'Staff member with this employee ID already exists in this business',
          );
        }
      }

      // Validate department and designation if provided
      if (updateStaffMemberDto.departmentId) {
        const departmentExists = await this.db
          .select()
          .from(departments)
          .where(
            and(
              eq(departments.id, updateStaffMemberDto.departmentId),
              eq(departments.businessId, businessId),
              isNull(departments.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!departmentExists) {
          throw new BadRequestException('Department not found');
        }
      }

      if (updateStaffMemberDto.designationId) {
        const designationExists = await this.db
          .select()
          .from(designations)
          .where(
            and(
              eq(designations.id, updateStaffMemberDto.designationId),
              eq(designations.businessId, businessId),
              isNull(designations.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!designationExists) {
          throw new BadRequestException('Designation not found');
        }
      }

      return await this.db.transaction(async (tx) => {
        let profileImageId: string | undefined;

        // Handle profile image upload
        if (profileImageFile) {
          profileImageId = await this.mediaService.updateMediaReference(
            existingStaff.profileImageId,
            profileImageFile,
            'staff/profile-images',
            businessId,
            userId,
          );
        }

        // Prepare update data
        const updateData: any = {
          updatedBy: userId,
          updatedAt: new Date(),
        };

        // Only update provided fields
        if (updateStaffMemberDto.departmentId !== undefined) {
          updateData.departmentId = updateStaffMemberDto.departmentId;
        }
        if (updateStaffMemberDto.designationId !== undefined) {
          updateData.designationId = updateStaffMemberDto.designationId;
        }
        if (updateStaffMemberDto.firstName !== undefined) {
          updateData.firstName = updateStaffMemberDto.firstName;
        }
        if (updateStaffMemberDto.lastName !== undefined) {
          updateData.lastName = updateStaffMemberDto.lastName;
        }
        if (updateStaffMemberDto.displayName !== undefined) {
          updateData.displayName = updateStaffMemberDto.displayName;
        }
        if (updateStaffMemberDto.email !== undefined) {
          updateData.email = updateStaffMemberDto.email;
        }
        if (updateStaffMemberDto.phone !== undefined) {
          updateData.phone = updateStaffMemberDto.phone;
        }
        if (updateStaffMemberDto.isUser !== undefined) {
          updateData.isUser = updateStaffMemberDto.isUser;
        }
        if (updateStaffMemberDto.employeeId !== undefined) {
          updateData.employeeId = updateStaffMemberDto.employeeId;
        }
        if (updateStaffMemberDto.dateOfBirth !== undefined) {
          updateData.dateOfBirth = updateStaffMemberDto.dateOfBirth
            ? new Date(updateStaffMemberDto.dateOfBirth)
            : null;
        }
        if (updateStaffMemberDto.dateOfJoining !== undefined) {
          updateData.dateOfJoining = updateStaffMemberDto.dateOfJoining
            ? new Date(updateStaffMemberDto.dateOfJoining)
            : null;
        }
        if (updateStaffMemberDto.employmentType !== undefined) {
          updateData.employmentType = updateStaffMemberDto.employmentType;
        }
        // Handle address update if provided
        if (updateStaffMemberDto.address) {
          const addressDto = updateStaffMemberDto.address;

          // Check if staff member has an existing address
          const existingStaff = await tx
            .select({ addressId: staffMembers.addressId })
            .from(staffMembers)
            .where(eq(staffMembers.id, id))
            .then((results) => results[0]);

          if (existingStaff?.addressId) {
            // Update existing address
            await this.addressService.update(
              userId,
              existingStaff.addressId,
              {
                street: addressDto.street,
                city: addressDto.city,
                state: addressDto.state,
                zipCode: addressDto.zipCode,
                country: addressDto.country,
                addressType: addressDto.addressType,
                isDefault: addressDto.isDefault,
              },
              businessId,
              tx,
            );
            updateData.addressId = existingStaff.addressId;
          } else {
            // Create new address
            const addressResult = await this.addressService.create(
              userId,
              {
                street: addressDto.street,
                city: addressDto.city,
                state: addressDto.state,
                zipCode: addressDto.zipCode,
                country: addressDto.country,
                addressType: addressDto.addressType,
                businessId,
                isDefault: addressDto.isDefault,
              },
              tx,
            );
            updateData.addressId = addressResult.id;
          }
        }
        if (updateStaffMemberDto.status !== undefined) {
          updateData.status = updateStaffMemberDto.status;
        }
        if (profileImageId) {
          updateData.profileImageId = profileImageId;
        }

        // Update staff member
        await tx
          .update(staffMembers)
          .set(updateData)
          .where(eq(staffMembers.id, id));

        // Update related data if provided
        if (updateStaffMemberDto.familyDetails) {
          const familyDto = updateStaffMemberDto.familyDetails;
          const familyUpdateData: any = {
            updatedBy: userId,
            updatedAt: new Date(),
          };

          if (familyDto.maritalStatus !== undefined) {
            familyUpdateData.maritalStatus = familyDto.maritalStatus;
          }
          if (familyDto.numberOfChildren !== undefined) {
            familyUpdateData.numberOfChildren = familyDto.numberOfChildren;
          }
          if (familyDto.isSpouseWorking !== undefined) {
            familyUpdateData.isSpouseWorking = familyDto.isSpouseWorking;
          }
          if (
            familyDto.spouseFirstName !== undefined ||
            familyDto.spouseName !== undefined
          ) {
            familyUpdateData.spouseFirstName =
              familyDto.spouseFirstName || familyDto.spouseName;
          }
          if (familyDto.spouseMiddleName !== undefined) {
            familyUpdateData.spouseMiddleName = familyDto.spouseMiddleName;
          }
          if (familyDto.spouseLastName !== undefined) {
            familyUpdateData.spouseLastName = familyDto.spouseLastName;
          }
          if (familyDto.spouseBirthDate !== undefined) {
            familyUpdateData.spouseBirthDate = familyDto.spouseBirthDate;
          }
          if (familyDto.spouseGender !== undefined) {
            familyUpdateData.spouseGender = familyDto.spouseGender;
          }
          if (familyDto.spouseEmail !== undefined) {
            familyUpdateData.spouseEmail = familyDto.spouseEmail;
          }
          if (familyDto.spousePhone !== undefined) {
            familyUpdateData.spousePhone = familyDto.spousePhone;
          }
          if (
            familyDto.notes !== undefined ||
            familyDto.spouseOccupation !== undefined ||
            familyDto.fatherName !== undefined ||
            familyDto.motherName !== undefined
          ) {
            familyUpdateData.notes =
              [
                familyDto.notes,
                familyDto.spouseOccupation
                  ? `Spouse Occupation: ${familyDto.spouseOccupation}`
                  : null,
                familyDto.fatherName
                  ? `Father's Name: ${familyDto.fatherName}`
                  : null,
                familyDto.motherName
                  ? `Mother's Name: ${familyDto.motherName}`
                  : null,
              ]
                .filter(Boolean)
                .join('; ') || undefined;
          }

          await tx
            .update(staffFamilyDetails)
            .set(familyUpdateData)
            .where(eq(staffFamilyDetails.staffMemberId, id));
        }

        // Update physical info if provided
        if (updateStaffMemberDto.physicalInfo) {
          const physicalDto = updateStaffMemberDto.physicalInfo;
          const physicalUpdateData: any = {
            updatedBy: userId,
            updatedAt: new Date(),
          };

          if (physicalDto.heightCm !== undefined) {
            physicalUpdateData.heightCm = physicalDto.heightCm;
          }
          if (physicalDto.weightKg !== undefined) {
            physicalUpdateData.weightKg = physicalDto.weightKg;
          }
          if (physicalDto.bloodType !== undefined) {
            physicalUpdateData.bloodType = physicalDto.bloodType;
          }
          if (physicalDto.visionLeft !== undefined) {
            physicalUpdateData.visionLeft = physicalDto.visionLeft;
          }
          if (physicalDto.visionRight !== undefined) {
            physicalUpdateData.visionRight = physicalDto.visionRight;
          }
          if (physicalDto.hearingLeft !== undefined) {
            physicalUpdateData.hearingLeft = physicalDto.hearingLeft;
          }
          if (physicalDto.hearingRight !== undefined) {
            physicalUpdateData.hearingRight = physicalDto.hearingRight;
          }
          if (physicalDto.handLeft !== undefined) {
            physicalUpdateData.handLeft = physicalDto.handLeft;
          }
          if (physicalDto.handRight !== undefined) {
            physicalUpdateData.handRight = physicalDto.handRight;
          }
          if (physicalDto.legLeft !== undefined) {
            physicalUpdateData.legLeft = physicalDto.legLeft;
          }
          if (physicalDto.legRight !== undefined) {
            physicalUpdateData.legRight = physicalDto.legRight;
          }

          // Check if physical info record exists
          const existingPhysicalInfo = await tx
            .select()
            .from(staffPhysicalInfo)
            .where(
              and(
                eq(staffPhysicalInfo.staffMemberId, id),
                isNull(staffPhysicalInfo.deletedAt),
              ),
            )
            .then((results) => results[0]);

          if (existingPhysicalInfo) {
            // Update existing record
            await tx
              .update(staffPhysicalInfo)
              .set(physicalUpdateData)
              .where(eq(staffPhysicalInfo.staffMemberId, id));
          } else {
            // Create new record
            await tx.insert(staffPhysicalInfo).values({
              businessId,
              staffMemberId: id,
              ...physicalUpdateData,
              createdBy: userId,
            });
          }
        }

        // Update emergency contacts if provided
        if (updateStaffMemberDto.emergencyContacts) {
          // First, soft delete existing emergency contacts
          await tx
            .update(staffEmergencyContacts)
            .set({
              deletedBy: userId,
              deletedAt: new Date(),
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(staffEmergencyContacts.staffMemberId, id),
                isNull(staffEmergencyContacts.deletedAt),
              ),
            );

          // Then create new emergency contacts
          for (const contactDto of updateStaffMemberDto.emergencyContacts) {
            await tx.insert(staffEmergencyContacts).values({
              businessId,
              staffMemberId: id,
              firstName: contactDto.firstName,
              middleName: contactDto.middleName,
              lastName: contactDto.lastName,
              relationship: contactDto.relationship,
              mobilePhone: contactDto.mobilePhone,
              housePhone: contactDto.housePhone,
              officePhone: contactDto.officePhone,
              addressId: contactDto.addressId,
              priority: contactDto.priority ?? 1,
              isActive: contactDto.isActive ?? true,
              notes: contactDto.notes,
              createdBy: userId,
            });
          }
        }

        // Handle document uploads during update
        if (documentFiles && documentFiles.length > 0) {
          // Use bulk upload with staff member ID as reference
          await this.mediaService.uploadMultipleMediaWithReference(
            documentFiles,
            'staff/documents',
            businessId,
            userId,
            id, // referenceId as staff member ID
          );
        }

        // Log activity
        await this.activityLogService.log(
          ActivityLogName.UPDATE,
          `Staff member "${updateStaffMemberDto.displayName || existingStaff.displayName}" was updated`,
          { id: id, type: 'staff_member' },
          { id: userId, type: 'user' },
          { staffMemberId: id, businessId },
        );

        return await this.mapToStaffMemberDto(
          { ...existingStaff, ...updateData, id },
          businessId,
        );
      });
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update staff member: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ success: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if staff member exists
    const existingStaff = await this.db
      .select()
      .from(staffMembers)
      .where(
        and(
          eq(staffMembers.id, id),
          eq(staffMembers.businessId, businessId),
          isNull(staffMembers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingStaff) {
      throw new NotFoundException('Staff member not found');
    }

    await this.db.transaction(async (tx) => {
      const now = new Date();

      // Soft delete staff member
      await tx
        .update(staffMembers)
        .set({
          deletedBy: userId,
          deletedAt: now,
          updatedBy: userId,
          updatedAt: now,
        })
        .where(eq(staffMembers.id, id));

      // Soft delete related records
      await tx
        .update(staffFamilyDetails)
        .set({
          deletedBy: userId,
          deletedAt: now,
          updatedBy: userId,
          updatedAt: now,
        })
        .where(eq(staffFamilyDetails.staffMemberId, id));

      // Documents are now handled by media service with referenceId
      // No need to soft delete staffDocuments records as they no longer exist

      await tx
        .update(staffEmergencyContacts)
        .set({
          deletedBy: userId,
          deletedAt: now,
          updatedBy: userId,
          updatedAt: now,
        })
        .where(eq(staffEmergencyContacts.staffMemberId, id));

      await tx
        .update(staffPhysicalInfo)
        .set({
          deletedBy: userId,
          deletedAt: now,
          updatedBy: userId,
          updatedAt: now,
        })
        .where(eq(staffPhysicalInfo.staffMemberId, id));

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Staff member "${existingStaff.displayName}" was deleted`,
        { id: id, type: 'staff_member' },
        { id: userId, type: 'user' },
        { staffMemberId: id, businessId },
      );
    });

    return {
      success: true,
      message: `Staff member with ID ${id} has been deleted`,
    };
  }

  async bulkCreate(
    bulkCreateStaffDto: BulkCreateStaffDto,
    businessId: string,
    userId: string,
  ): Promise<{
    ids: string[];
    message: string;
    count: number;
  }> {
    const createdIds: string[] = [];

    await this.db.transaction(async (tx) => {
      for (const staffDto of bulkCreateStaffDto.staffMembers) {
        // Check for duplicates
        const existingStaff = await tx
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.businessId, businessId),
              or(
                eq(staffMembers.email, staffDto.email),
                eq(staffMembers.displayName, staffDto.displayName),
                staffDto.employeeId
                  ? eq(staffMembers.employeeId, staffDto.employeeId)
                  : sql`false`,
              ),
              isNull(staffMembers.deletedAt),
            ),
          )
          .limit(1);

        if (existingStaff.length > 0) {
          throw new ConflictException(
            `Staff member with email ${staffDto.email} or display name ${staffDto.displayName} already exists`,
          );
        }

        // Handle address creation if provided
        let addressId: string | undefined;
        if (staffDto.address) {
          const addressResult = await this.addressService.create(
            userId,
            {
              street: staffDto.address.street,
              city: staffDto.address.city,
              state: staffDto.address.state,
              zipCode: staffDto.address.zipCode,
              country: staffDto.address.country,
              addressType: staffDto.address.addressType,
              businessId,
              isDefault: staffDto.address.isDefault,
            },
            tx,
          );
          addressId = addressResult.id;
        }

        // Create staff member
        const [staffMember] = await tx
          .insert(staffMembers)
          .values({
            businessId,
            departmentId: staffDto.departmentId,
            designationId: staffDto.designationId,
            firstName: staffDto.firstName,
            lastName: staffDto.lastName,
            displayName: staffDto.displayName,
            email: staffDto.email,
            phone: staffDto.phone,
            isUser: staffDto.isUser ?? false,
            employeeId: staffDto.employeeId,
            dateOfBirth: staffDto.dateOfBirth
              ? new Date(staffDto.dateOfBirth)
              : undefined,
            dateOfJoining: staffDto.dateOfJoining
              ? new Date(staffDto.dateOfJoining)
              : undefined,
            employmentType: staffDto.employmentType ?? EmploymentType.FULL_TIME,
            addressId: addressId,
            status: staffDto.status ?? EmploymentStatus.ACTIVE,
            createdBy: userId,
          })
          .returning();

        createdIds.push(staffMember.id);

        // Create related records if provided
        if (staffDto.familyDetails) {
          const familyDto = staffDto.familyDetails;
          await tx.insert(staffFamilyDetails).values({
            businessId,
            staffMemberId: staffMember.id,
            maritalStatus: familyDto.maritalStatus,
            numberOfChildren: familyDto.numberOfChildren ?? 0,
            isSpouseWorking: familyDto.isSpouseWorking,
            // Handle legacy spouseName field - use it as spouseFirstName if provided
            spouseFirstName: familyDto.spouseFirstName || familyDto.spouseName,
            spouseMiddleName: familyDto.spouseMiddleName,
            spouseLastName: familyDto.spouseLastName,
            spouseBirthDate: familyDto.spouseBirthDate,
            spouseGender: familyDto.spouseGender,
            spouseEmail: familyDto.spouseEmail,
            spousePhone: familyDto.spousePhone,
            // Handle legacy fields - store in notes if provided
            notes:
              [
                familyDto.notes,
                familyDto.spouseOccupation
                  ? `Spouse Occupation: ${familyDto.spouseOccupation}`
                  : null,
                familyDto.fatherName
                  ? `Father's Name: ${familyDto.fatherName}`
                  : null,
                familyDto.motherName
                  ? `Mother's Name: ${familyDto.motherName}`
                  : null,
              ]
                .filter(Boolean)
                .join('; ') || undefined,
            createdBy: userId,
          });
        }

        if (staffDto.emergencyContacts) {
          for (const contactDto of staffDto.emergencyContacts) {
            await tx.insert(staffEmergencyContacts).values({
              businessId,
              staffMemberId: staffMember.id,
              firstName: contactDto.firstName,
              middleName: contactDto.middleName,
              lastName: contactDto.lastName,
              relationship: contactDto.relationship,
              mobilePhone: contactDto.mobilePhone,
              housePhone: contactDto.housePhone,
              officePhone: contactDto.officePhone,
              addressId: contactDto.addressId,
              priority: contactDto.priority ?? 1,
              isActive: contactDto.isActive ?? true,
              notes: contactDto.notes,
              createdBy: userId,
            });
          }
        }
      }

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Bulk created ${createdIds.length} staff members`,
        { id: createdIds.join(','), type: 'staff_member' },
        { id: userId, type: 'user' },
        { staffMemberIds: createdIds, businessId },
      );
    });

    return {
      ids: createdIds,
      message: 'Staff members created successfully',
      count: createdIds.length,
    };
  }

  async bulkDelete(
    bulkDeleteStaffDto: BulkDeleteStaffDto,
    businessId: string,
    userId: string,
  ): Promise<BulkDeleteStaffResponseDto> {
    // Check if all staff members exist
    const existingStaff = await this.db
      .select({ id: staffMembers.id, displayName: staffMembers.displayName })
      .from(staffMembers)
      .where(
        and(
          inArray(staffMembers.id, bulkDeleteStaffDto.ids),
          eq(staffMembers.businessId, businessId),
          isNull(staffMembers.deletedAt),
        ),
      );

    if (existingStaff.length !== bulkDeleteStaffDto.ids.length) {
      const foundIds = existingStaff.map((staff) => staff.id);
      const missingIds = bulkDeleteStaffDto.ids.filter(
        (id) => !foundIds.includes(id),
      );
      throw new NotFoundException(
        `Staff members not found: ${missingIds.join(', ')}`,
      );
    }

    const deletedIds: string[] = [];

    await this.db.transaction(async (tx) => {
      const now = new Date();

      // Soft delete staff members
      await tx
        .update(staffMembers)
        .set({
          deletedBy: userId,
          deletedAt: now,
          updatedBy: userId,
          updatedAt: now,
        })
        .where(inArray(staffMembers.id, bulkDeleteStaffDto.ids));

      // Soft delete related records
      await tx
        .update(staffFamilyDetails)
        .set({
          deletedBy: userId,
          deletedAt: now,
          updatedBy: userId,
          updatedAt: now,
        })
        .where(
          inArray(staffFamilyDetails.staffMemberId, bulkDeleteStaffDto.ids),
        );

      // Documents are now handled by media service with referenceId
      // No need to soft delete staffDocuments records as they no longer exist

      await tx
        .update(staffEmergencyContacts)
        .set({
          deletedBy: userId,
          deletedAt: now,
          updatedBy: userId,
          updatedAt: now,
        })
        .where(
          inArray(staffEmergencyContacts.staffMemberId, bulkDeleteStaffDto.ids),
        );

      await tx
        .update(staffPhysicalInfo)
        .set({
          deletedBy: userId,
          deletedAt: now,
          updatedBy: userId,
          updatedAt: now,
        })
        .where(
          inArray(staffPhysicalInfo.staffMemberId, bulkDeleteStaffDto.ids),
        );

      deletedIds.push(...bulkDeleteStaffDto.ids);

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Bulk deleted ${deletedIds.length} staff members: ${existingStaff.map((s) => s.displayName).join(', ')}`,
        { id: deletedIds.join(','), type: 'staff_member' },
        { id: userId, type: 'user' },
        { staffMemberIds: deletedIds, businessId },
      );
    });

    return {
      deletedIds,
      message: 'Staff members deleted successfully',
      count: deletedIds.length,
    };
  }

  async findSlim(businessId: string): Promise<StaffMemberSlimDto[]> {
    const staffList = await this.db
      .select({
        id: staffMembers.id,
        displayName: staffMembers.displayName,
        profileImageUrl: media.publicUrl,
      })
      .from(staffMembers)
      .leftJoin(media, eq(staffMembers.profileImageId, media.id))
      .where(
        and(
          eq(staffMembers.businessId, businessId),
          eq(staffMembers.status, EmploymentStatus.ACTIVE),
          isNull(staffMembers.deletedAt),
        ),
      )
      .orderBy(desc(staffMembers.createdAt));

    return staffList.map((staff) => ({
      id: staff.id,
      displayName: staff.displayName,
      profileImageUrl: staff.profileImageUrl,
    }));
  }

  async findAutocomplete(
    businessId: string,
    search?: string,
    limit: number = 10,
  ): Promise<StaffMemberAutocompleteDto[]> {
    const conditions = [
      eq(staffMembers.businessId, businessId),
      eq(staffMembers.status, EmploymentStatus.ACTIVE),
      isNull(staffMembers.deletedAt),
    ];

    if (search && search.trim()) {
      conditions.push(ilike(staffMembers.displayName, `%${search.trim()}%`));
    }

    const staffList = await this.db
      .select({
        id: staffMembers.id,
        displayName: staffMembers.displayName,
        profileImageUrl: media.publicUrl,
      })
      .from(staffMembers)
      .leftJoin(media, eq(staffMembers.profileImageId, media.id))
      .where(and(...conditions))
      .orderBy(desc(staffMembers.createdAt))
      .limit(limit);

    return staffList.map((staff) => ({
      id: staff.id,
      name: staff.displayName,
      profileImage: staff.profileImageUrl,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createStaffMemberDto: CreateStaffMemberDto,
    profileImageFile?: Express.Multer.File,
    documentFiles?: Express.Multer.File[],
  ): Promise<{ id: string }> {
    const staffMember = await this.create(
      userId,
      businessId,
      createStaffMemberDto,
      profileImageFile,
      documentFiles,
    );
    return { id: staffMember.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateStaffMemberDto: UpdateStaffMemberDto,
    profileImageFile?: Express.Multer.File,
    documentFiles?: Express.Multer.File[],
  ): Promise<{ id: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateStaffMemberDto,
      profileImageFile,
      documentFiles,
    );
    return { id };
  }

  async bulkCreateAndReturnIds(
    bulkCreateStaffDto: BulkCreateStaffDto,
    businessId: string,
    userId: string,
  ): Promise<{ ids: string[] }> {
    const result = await this.bulkCreate(
      bulkCreateStaffDto,
      businessId,
      userId,
    );
    return { ids: result.ids };
  }

  // Physical Info Management Methods
  async createStaffPhysicalInfo(
    userId: string,
    businessId: string,
    staffMemberId: string,
    physicalInfoDto: any,
  ): Promise<StaffPhysicalInfoDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if staff member exists
    const existingStaff = await this.db
      .select()
      .from(staffMembers)
      .where(
        and(
          eq(staffMembers.id, staffMemberId),
          eq(staffMembers.businessId, businessId),
          isNull(staffMembers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingStaff) {
      throw new NotFoundException('Staff member not found');
    }

    // Check if physical info already exists
    const existingPhysicalInfo = await this.db
      .select()
      .from(staffPhysicalInfo)
      .where(
        and(
          eq(staffPhysicalInfo.staffMemberId, staffMemberId),
          isNull(staffPhysicalInfo.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (existingPhysicalInfo) {
      throw new ConflictException(
        'Physical information already exists for this staff member',
      );
    }

    const [physicalRecord] = await this.db
      .insert(staffPhysicalInfo)
      .values({
        businessId,
        staffMemberId,
        heightCm: physicalInfoDto.heightCm,
        weightKg: physicalInfoDto.weightKg,
        bloodType: physicalInfoDto.bloodType,
        visionLeft: physicalInfoDto.visionLeft,
        visionRight: physicalInfoDto.visionRight,
        hearingLeft: physicalInfoDto.hearingLeft,
        hearingRight: physicalInfoDto.hearingRight,
        handLeft: physicalInfoDto.handLeft,
        handRight: physicalInfoDto.handRight,
        legLeft: physicalInfoDto.legLeft,
        legRight: physicalInfoDto.legRight,
        createdBy: userId,
      })
      .returning();

    // Log activity
    await this.activityLogService.log(
      ActivityLogName.CREATE,
      `Physical information created for staff member "${existingStaff.displayName}"`,
      { id: physicalRecord.id, type: 'staff_physical_info' },
      { id: userId, type: 'user' },
      { staffMemberId, businessId },
    );

    return this.mapToStaffPhysicalInfoDto(physicalRecord);
  }

  async updateStaffPhysicalInfo(
    userId: string,
    businessId: string,
    staffMemberId: string,
    physicalInfoDto: any,
  ): Promise<StaffPhysicalInfoDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if staff member exists
    const existingStaff = await this.db
      .select()
      .from(staffMembers)
      .where(
        and(
          eq(staffMembers.id, staffMemberId),
          eq(staffMembers.businessId, businessId),
          isNull(staffMembers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingStaff) {
      throw new NotFoundException('Staff member not found');
    }

    // Check if physical info exists
    const existingPhysicalInfo = await this.db
      .select()
      .from(staffPhysicalInfo)
      .where(
        and(
          eq(staffPhysicalInfo.staffMemberId, staffMemberId),
          isNull(staffPhysicalInfo.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingPhysicalInfo) {
      throw new NotFoundException(
        'Physical information not found for this staff member',
      );
    }

    const updateData: any = {
      updatedBy: userId,
      updatedAt: new Date(),
    };

    if (physicalInfoDto.heightCm !== undefined) {
      updateData.heightCm = physicalInfoDto.heightCm;
    }
    if (physicalInfoDto.weightKg !== undefined) {
      updateData.weightKg = physicalInfoDto.weightKg;
    }
    if (physicalInfoDto.bloodType !== undefined) {
      updateData.bloodType = physicalInfoDto.bloodType;
    }
    if (physicalInfoDto.visionLeft !== undefined) {
      updateData.visionLeft = physicalInfoDto.visionLeft;
    }
    if (physicalInfoDto.visionRight !== undefined) {
      updateData.visionRight = physicalInfoDto.visionRight;
    }
    if (physicalInfoDto.hearingLeft !== undefined) {
      updateData.hearingLeft = physicalInfoDto.hearingLeft;
    }
    if (physicalInfoDto.hearingRight !== undefined) {
      updateData.hearingRight = physicalInfoDto.hearingRight;
    }
    if (physicalInfoDto.handLeft !== undefined) {
      updateData.handLeft = physicalInfoDto.handLeft;
    }
    if (physicalInfoDto.handRight !== undefined) {
      updateData.handRight = physicalInfoDto.handRight;
    }
    if (physicalInfoDto.legLeft !== undefined) {
      updateData.legLeft = physicalInfoDto.legLeft;
    }
    if (physicalInfoDto.legRight !== undefined) {
      updateData.legRight = physicalInfoDto.legRight;
    }

    const [updatedRecord] = await this.db
      .update(staffPhysicalInfo)
      .set(updateData)
      .where(eq(staffPhysicalInfo.id, existingPhysicalInfo.id))
      .returning();

    // Log activity
    await this.activityLogService.log(
      ActivityLogName.UPDATE,
      `Physical information updated for staff member "${existingStaff.displayName}"`,
      { id: updatedRecord.id, type: 'staff_physical_info' },
      { id: userId, type: 'user' },
      { staffMemberId, businessId },
    );

    return this.mapToStaffPhysicalInfoDto(updatedRecord);
  }

  async getStaffPhysicalInfo(
    businessId: string,
    staffMemberId: string,
  ): Promise<StaffPhysicalInfoDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const physicalInfo = await this.db
      .select({
        id: staffPhysicalInfo.id,
        businessId: staffPhysicalInfo.businessId,
        staffMemberId: staffPhysicalInfo.staffMemberId,
        heightCm: staffPhysicalInfo.heightCm,
        weightKg: staffPhysicalInfo.weightKg,
        bloodType: staffPhysicalInfo.bloodType,
        visionLeft: staffPhysicalInfo.visionLeft,
        visionRight: staffPhysicalInfo.visionRight,
        hearingLeft: staffPhysicalInfo.hearingLeft,
        hearingRight: staffPhysicalInfo.hearingRight,
        handLeft: staffPhysicalInfo.handLeft,
        handRight: staffPhysicalInfo.handRight,
        legLeft: staffPhysicalInfo.legLeft,
        legRight: staffPhysicalInfo.legRight,
        createdBy: staffPhysicalInfo.createdBy,
        updatedBy: staffPhysicalInfo.updatedBy,
        createdAt: staffPhysicalInfo.createdAt,
        updatedAt: staffPhysicalInfo.updatedAt,
        createdByName: users.displayName,
      })
      .from(staffPhysicalInfo)
      .leftJoin(users, eq(staffPhysicalInfo.createdBy, users.id))
      .where(
        and(
          eq(staffPhysicalInfo.staffMemberId, staffMemberId),
          eq(staffPhysicalInfo.businessId, businessId),
          isNull(staffPhysicalInfo.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!physicalInfo) {
      throw new NotFoundException(
        'Physical information not found for this staff member',
      );
    }

    return this.mapToStaffPhysicalInfoDto(physicalInfo);
  }

  async deleteStaffPhysicalInfo(
    userId: string,
    businessId: string,
    staffMemberId: string,
  ): Promise<{ success: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if staff member exists
    const existingStaff = await this.db
      .select()
      .from(staffMembers)
      .where(
        and(
          eq(staffMembers.id, staffMemberId),
          eq(staffMembers.businessId, businessId),
          isNull(staffMembers.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingStaff) {
      throw new NotFoundException('Staff member not found');
    }

    // Check if physical info exists
    const existingPhysicalInfo = await this.db
      .select()
      .from(staffPhysicalInfo)
      .where(
        and(
          eq(staffPhysicalInfo.staffMemberId, staffMemberId),
          isNull(staffPhysicalInfo.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingPhysicalInfo) {
      throw new NotFoundException(
        'Physical information not found for this staff member',
      );
    }

    const now = new Date();
    await this.db
      .update(staffPhysicalInfo)
      .set({
        deletedBy: userId,
        deletedAt: now,
        updatedBy: userId,
        updatedAt: now,
      })
      .where(eq(staffPhysicalInfo.id, existingPhysicalInfo.id));

    // Log activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Physical information deleted for staff member "${existingStaff.displayName}"`,
      { id: existingPhysicalInfo.id, type: 'staff_physical_info' },
      { id: userId, type: 'user' },
      { staffMemberId, businessId },
    );

    return {
      success: true,
      message: 'Physical information deleted successfully',
    };
  }

  private mapToStaffPhysicalInfoDto(physicalInfo: any): StaffPhysicalInfoDto {
    return {
      id: physicalInfo.id,
      businessId: physicalInfo.businessId,
      staffMemberId: physicalInfo.staffMemberId,
      heightCm: physicalInfo.heightCm,
      weightKg: physicalInfo.weightKg,
      bloodType: physicalInfo.bloodType,
      visionLeft: physicalInfo.visionLeft,
      visionRight: physicalInfo.visionRight,
      hearingLeft: physicalInfo.hearingLeft,
      hearingRight: physicalInfo.hearingRight,
      handLeft: physicalInfo.handLeft,
      handRight: physicalInfo.handRight,
      legLeft: physicalInfo.legLeft,
      legRight: physicalInfo.legRight,
      createdBy: physicalInfo.createdByName || physicalInfo.createdBy,
      updatedBy: physicalInfo.updatedByName || physicalInfo.updatedBy,
      createdAt: physicalInfo.createdAt,
      updatedAt: physicalInfo.updatedAt,
    };
  }

  // Staff Leave Type Assignment Methods
  async assignStaffLeaveType(
    userId: string,
    businessId: string | null,
    assignStaffLeaveTypeDto: AssignStaffLeaveTypeDto,
  ): Promise<StaffLeaveTypeIdResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate staff member exists
      const staffMember = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, assignStaffLeaveTypeDto.staffId),
            eq(staffMembers.businessId, businessId),
            isNull(staffMembers.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!staffMember) {
        throw new NotFoundException('Staff member not found');
      }

      // Validate leave type exists
      const leaveType = await this.db
        .select()
        .from(leaveTypes)
        .where(
          and(
            eq(leaveTypes.id, assignStaffLeaveTypeDto.leaveTypeId),
            eq(leaveTypes.businessId, businessId),
            isNull(leaveTypes.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!leaveType) {
        throw new NotFoundException('Leave type not found');
      }

      // Check if assignment already exists
      const existingAssignment = await this.db
        .select()
        .from(staffLeaveTypes)
        .where(
          and(
            eq(staffLeaveTypes.businessId, businessId),
            eq(staffLeaveTypes.staffId, assignStaffLeaveTypeDto.staffId),
            eq(
              staffLeaveTypes.leaveTypeId,
              assignStaffLeaveTypeDto.leaveTypeId,
            ),
            isNull(staffLeaveTypes.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingAssignment) {
        throw new ConflictException(
          `Leave type ${leaveType.leaveName} is already assigned to ${staffMember.displayName}`,
        );
      }

      // Create the assignment
      const [newAssignment] = await this.db
        .insert(staffLeaveTypes)
        .values({
          businessId,
          staffId: assignStaffLeaveTypeDto.staffId,
          leaveTypeId: assignStaffLeaveTypeDto.leaveTypeId,
          isActive: assignStaffLeaveTypeDto.isActive ?? true,
          createdBy: userId,
        })
        .returning();

      // Auto-create leave balance for the current year
      const currentYear = new Date().getFullYear();
      try {
        await this.leaveBalancesService.create(userId, businessId, {
          employeeId: assignStaffLeaveTypeDto.staffId,
          leaveTypeId: assignStaffLeaveTypeDto.leaveTypeId,
          year: currentYear,
          entitledDays: leaveType.daysAllowedPerYear,
          usedDays: '0.00',
          remainingDays: leaveType.daysAllowedPerYear,
          carriedForward: '0.00',
        });
      } catch (leaveBalanceError) {
        // Log warning but don't fail the assignment if leave balance creation fails
        console.warn(
          `Failed to create leave balance for staff ${staffMember.displayName} and leave type ${leaveType.leaveName}: ${(leaveBalanceError as Error).message}`,
        );
      }

      // Log the assignment
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Leave type ${leaveType.leaveName} assigned to ${staffMember.displayName}`,
        { id: newAssignment.id, type: 'staff-leave-type' },
        { id: userId, type: 'user' },
        { assignmentId: newAssignment.id, businessId },
      );

      return { id: newAssignment.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to assign leave type to staff member',
      );
    }
  }

  async bulkAssignStaffLeaveTypes(
    userId: string,
    businessId: string | null,
    bulkAssignDto: BulkAssignStaffLeaveTypesDto,
  ): Promise<BulkAssignStaffLeaveTypeResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!bulkAssignDto.assignments || bulkAssignDto.assignments.length === 0) {
      throw new BadRequestException('No assignments provided');
    }

    const assignmentIds: string[] = [];
    let assignedCount = 0;

    try {
      for (const assignment of bulkAssignDto.assignments) {
        try {
          const result = await this.assignStaffLeaveType(
            userId,
            businessId,
            assignment,
          );
          assignmentIds.push(result.id);
          assignedCount++;
        } catch (assignmentError) {
          // Log error but continue with other assignments
          console.warn(
            `Failed to assign leave type: ${(assignmentError as Error).message}`,
          );
        }
      }

      return {
        message: 'Staff leave types assigned successfully',
        assignedCount,
        assignmentIds,
      };
    } catch {
      throw new BadRequestException('Failed to process bulk assignment');
    }
  }

  async getStaffLeaveTypes(
    businessId: string | null,
    page: number = 1,
    limit: number = 10,
    staffId?: string,
    leaveTypeId?: string,
    isActive?: boolean,
  ): Promise<PaginatedStaffLeaveTypesResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(staffLeaveTypes.businessId, businessId),
      isNull(staffLeaveTypes.deletedAt),
    ];

    if (staffId) {
      whereConditions.push(eq(staffLeaveTypes.staffId, staffId));
    }

    if (leaveTypeId) {
      whereConditions.push(eq(staffLeaveTypes.leaveTypeId, leaveTypeId));
    }

    if (isActive !== undefined) {
      whereConditions.push(eq(staffLeaveTypes.isActive, isActive));
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(staffLeaveTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Get paginated data with joins
    const result = await this.db
      .select({
        assignment: staffLeaveTypes,
        staff: {
          id: staffMembers.id,
          displayName: staffMembers.displayName,
        },
        leaveType: {
          id: leaveTypes.id,
          leaveName: leaveTypes.leaveName,
          leaveCode: leaveTypes.leaveCode,
        },
      })
      .from(staffLeaveTypes)
      .innerJoin(staffMembers, eq(staffLeaveTypes.staffId, staffMembers.id))
      .innerJoin(leaveTypes, eq(staffLeaveTypes.leaveTypeId, leaveTypes.id))
      .where(and(...whereConditions))
      .orderBy(desc(staffLeaveTypes.createdAt))
      .limit(limit)
      .offset(offset);

    const data = await Promise.all(
      result.map(async (item) => this.mapToStaffLeaveTypeDto(item)),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async getStaffLeaveTypeById(
    businessId: string | null,
    assignmentId: string,
  ): Promise<StaffLeaveTypeDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        assignment: staffLeaveTypes,
        staff: {
          id: staffMembers.id,
          displayName: staffMembers.displayName,
        },
        leaveType: {
          id: leaveTypes.id,
          leaveName: leaveTypes.leaveName,
          leaveCode: leaveTypes.leaveCode,
        },
      })
      .from(staffLeaveTypes)
      .innerJoin(staffMembers, eq(staffLeaveTypes.staffId, staffMembers.id))
      .innerJoin(leaveTypes, eq(staffLeaveTypes.leaveTypeId, leaveTypes.id))
      .where(
        and(
          eq(staffLeaveTypes.id, assignmentId),
          eq(staffLeaveTypes.businessId, businessId),
          isNull(staffLeaveTypes.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Staff leave type assignment not found');
    }

    return await this.mapToStaffLeaveTypeDto(result);
  }

  async updateStaffLeaveType(
    userId: string,
    businessId: string | null,
    assignmentId: string,
    updateDto: UpdateStaffLeaveTypeDto,
  ): Promise<StaffLeaveTypeIdResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if assignment exists
    const existingAssignment = await this.db
      .select()
      .from(staffLeaveTypes)
      .where(
        and(
          eq(staffLeaveTypes.id, assignmentId),
          eq(staffLeaveTypes.businessId, businessId),
          isNull(staffLeaveTypes.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingAssignment) {
      throw new NotFoundException('Staff leave type assignment not found');
    }

    // Update the assignment
    const [updatedAssignment] = await this.db
      .update(staffLeaveTypes)
      .set({
        ...updateDto,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(staffLeaveTypes.id, assignmentId))
      .returning();

    // Log the update
    await this.activityLogService.log(
      ActivityLogName.UPDATE,
      'Staff leave type assignment updated',
      { id: updatedAssignment.id, type: 'staff-leave-type' },
      { id: userId, type: 'user' },
      { assignmentId: updatedAssignment.id, businessId, changes: updateDto },
    );

    return { id: updatedAssignment.id };
  }

  async removeStaffLeaveType(
    userId: string,
    businessId: string | null,
    assignmentId: string,
  ): Promise<DeleteStaffLeaveTypeResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if assignment exists
    const existingAssignment = await this.db
      .select()
      .from(staffLeaveTypes)
      .where(
        and(
          eq(staffLeaveTypes.id, assignmentId),
          eq(staffLeaveTypes.businessId, businessId),
          isNull(staffLeaveTypes.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingAssignment) {
      throw new NotFoundException('Staff leave type assignment not found');
    }

    // Soft delete the assignment
    await this.db
      .update(staffLeaveTypes)
      .set({
        deletedAt: new Date(),
        deletedBy: userId,
      })
      .where(eq(staffLeaveTypes.id, assignmentId));

    // Log the deletion
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      'Staff leave type assignment deleted',
      { id: assignmentId, type: 'staff-leave-type' },
      { id: userId, type: 'user' },
      { assignmentId, businessId },
    );

    return {
      message: 'Staff leave type assignment deleted successfully',
    };
  }

  private async mapToStaffLeaveTypeDto(data: any): Promise<StaffLeaveTypeDto> {
    const { assignment, staff, leaveType } = data;

    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      assignment.createdBy,
    );
    let updatedByName: string | undefined;
    if (assignment.updatedBy) {
      updatedByName = await this.usersService.getUserName(assignment.updatedBy);
    }

    return {
      id: assignment.id,
      businessId: assignment.businessId,
      staffId: assignment.staffId,
      staffDisplayName: staff.displayName,
      leaveTypeId: assignment.leaveTypeId,
      leaveTypeName: leaveType.leaveName,
      leaveTypeCode: leaveType.leaveCode,
      isActive: assignment.isActive,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: assignment.createdAt,
      updatedAt: assignment.updatedAt,
    };
  }

  private async getAuditUserInfo(userId: string): Promise<AuditUserDto> {
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
      with: {
        profileImageMedia: true,
      },
    });

    if (!user) {
      return {
        id: userId,
        firstName: 'Unknown',
        lastName: 'User',
        avatar: undefined,
      };
    }

    let avatarUrl: string | undefined;
    if (user.profileImageMedia) {
      try {
        avatarUrl = await this.mediaService.generateSignedUrlForMedia(
          user.profileImageMedia.id,
          user.activeBusinessId || '',
          'user/profile-images',
          60,
        );
      } catch (error) {
        console.warn(
          `Failed to generate avatar URL for user ${userId}:`,
          error.message,
        );
        avatarUrl = user.profileImageMedia.publicUrl || undefined;
      }
    }

    return {
      id: user.id,
      firstName: user.firstName || 'Unknown',
      lastName: user.lastName || 'User',
      avatar: avatarUrl,
    };
  }
}
