"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Image as ImageIcon,
  Check,
  X,
  Loader2,
  Tags,
  ShoppingCart,
  Wrench,
  Monitor,
  DollarSign,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { createAssetSchema, updateAssetSchema } from "@/lib/assets/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  AssetTableData,
  AssetStatus,
  AssetType,
  CreateAssetDto,
  UpdateAssetDto,
} from "@/types/asset";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileUploader,
  type UploadedFile,
} from "@/components/shared/file-uploader";
import {
  useAssetNameAvailability,
  useCreateAsset,
  useUpdateAsset,
  useAsset,
} from "@/lib/assets/hooks";
import { useCategoriesSlim } from "@/lib/categories/hooks";
import { useAccountsSlim } from "@/lib/accounts/hooks";
import { useSuppliersSlim } from "@/lib/suppliers/queries";
import {
  ASSET_TYPE_KEY_MAPPINGS,
  getAssetFieldLabel,
} from "@/lib/assets/utils";

// Form data structure for UI
interface AssetFormData {
  assetCode: string;
  name: string;
  type: AssetType;
  supplierId?: string;
  categoryId?: string;
  subCategoryId?: string;
  purchaseDate?: string;
  purchasePrice?: string;
  purchaseOrderNumber?: string;
  maintenanceFrequencyValue?: number;
  maintenanceFrequency?: string;
  lastMaintenanceDate?: string;
  maintenanceDueDate?: string;
  warrantyExpiryDate?: string;
  warrantyPeriod?: string;
  lifecycleExpiryDate?: string;
  bookValue?: string;
  fixedAssetAccountId?: string;
  depreciationAccountId?: string;
  expenseAccountId?: string;
  description?: string;
  notes?: string;
  status: AssetStatus;
  isAllocatedToAllLocations?: boolean;
  // Dynamic fields based on asset type
  key1?: string;
  key2?: string;
  key3?: string;
  key4?: string;
  key5?: string;
  key6?: string;
  key7?: string;
  key8?: string;
  key9?: string;
  key10?: string;
}

interface AssetSheetProps {
  asset?: AssetTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  isDemo?: boolean;
  isUpdate?: boolean;
}

export function AssetSheet({
  asset = null,
  open = false,
  onOpenChange,
  onSuccess,
  isDemo = false,
  isUpdate = false,
}: AssetSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);

  // Debounced values for availability checking
  const [debouncedName, setDebouncedName] = React.useState<string>("");

  // Mutation hooks for create and update operations
  const createAssetMutation = useCreateAsset(isDemo);
  const updateAssetMutation = useUpdateAsset(isDemo);

  // Get full asset data for updates
  const { data: fullAssetResponse } = useAsset(asset?.id || "", isDemo);
  const fullAsset = fullAssetResponse?.data;

  // Categories data for dropdowns
  const { data: categoriesResponse, isLoading: isLoadingCategories } =
    useCategoriesSlim(isDemo);

  const categories = categoriesResponse?.data || [];

  // Accounts slim data for dropdowns
  const { data: accountsResponse, isLoading: isLoadingAccounts } =
    useAccountsSlim(isDemo);

  const accounts = accountsResponse?.data || [];

  // Suppliers slim data for dropdowns
  const { data: suppliersResponse, isLoading: isLoadingSuppliers } =
    useSuppliersSlim(isDemo);

  const suppliers = suppliersResponse?.data || [];

  // Availability checks (only check if not updating the same asset)
  const shouldCheckNameAvailability =
    debouncedName.length > 0 &&
    (!isUpdate ||
      (asset && debouncedName.toLowerCase() !== asset.name.toLowerCase()));

  const { data: nameAvailabilityResponse, isLoading: isCheckingName } =
    useAssetNameAvailability(
      debouncedName,
      isDemo,
      isUpdate && asset ? asset.id : undefined
    );

  const isNameAvailable = shouldCheckNameAvailability
    ? nameAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<AssetFormData>({
    defaultValues: {
      assetCode: "",
      name: "",
      type: AssetType.OTHER,
      supplierId: undefined,
      categoryId: undefined,
      subCategoryId: undefined,
      purchaseDate: "",
      purchasePrice: "",
      purchaseOrderNumber: "",
      maintenanceFrequencyValue: undefined,
      maintenanceFrequency: "",
      lastMaintenanceDate: "",
      maintenanceDueDate: "",
      warrantyExpiryDate: "",
      warrantyPeriod: "",
      lifecycleExpiryDate: "",
      bookValue: "",
      fixedAssetAccountId: undefined,
      depreciationAccountId: undefined,
      expenseAccountId: undefined,
      description: "",
      notes: "",
      status: AssetStatus.AVAILABLE,
      isAllocatedToAllLocations: false,
      // Dynamic fields
      key1: "",
      key2: "",
      key3: "",
      key4: "",
      key5: "",
      key6: "",
      key7: "",
      key8: "",
      key9: "",
      key10: "",
    },
  });

  const selectedType = form.watch("type");

  // Get the selected image file for API calls
  const selectedImage =
    uploadedFiles.length > 0 && !uploadedFiles[0].id.startsWith("existing-")
      ? uploadedFiles[0].file
      : null;

  // Reset form when asset changes or sheet opens/closes
  React.useEffect(() => {
    if (open) {
      if (fullAsset && isUpdate) {
        // Populate form with existing asset data
        form.reset({
          assetCode: fullAsset.assetCode || "",
          name: fullAsset.name || "",
          type: fullAsset.type || AssetType.OTHER,
          supplierId: fullAsset.supplierId || undefined,
          categoryId: fullAsset.categoryId || undefined,
          subCategoryId: fullAsset.subCategoryId || undefined,
          purchaseDate: fullAsset.purchaseDate || "",
          purchasePrice: fullAsset.purchasePrice || "",
          purchaseOrderNumber: fullAsset.purchaseOrderNumber || "",
          maintenanceFrequencyValue:
            fullAsset.maintenanceFrequencyValue || undefined,
          maintenanceFrequency: fullAsset.maintenanceFrequency || "",
          lastMaintenanceDate: fullAsset.lastMaintenanceDate || "",
          maintenanceDueDate: fullAsset.maintenanceDueDate || "",
          warrantyExpiryDate: fullAsset.warrantyExpiryDate || "",
          warrantyPeriod: fullAsset.warrantyPeriod || "",
          lifecycleExpiryDate: fullAsset.lifecycleExpiryDate || "",
          bookValue: fullAsset.bookValue || "",
          fixedAssetAccountId: fullAsset.fixedAssetAccountId || undefined,
          depreciationAccountId: fullAsset.depreciationAccountId || undefined,
          expenseAccountId: fullAsset.expenseAccountId || undefined,
          description: fullAsset.description || "",
          notes: fullAsset.notes || "",
          status: fullAsset.status || AssetStatus.AVAILABLE,
          isAllocatedToAllLocations:
            fullAsset.isAllocatedToAllLocations || false,
          // Dynamic fields
          key1: fullAsset.key1 || "",
          key2: fullAsset.key2 || "",
          key3: fullAsset.key3 || "",
          key4: fullAsset.key4 || "",
          key5: fullAsset.key5 || "",
          key6: fullAsset.key6 || "",
          key7: fullAsset.key7 || "",
          key8: fullAsset.key8 || "",
          key9: fullAsset.key9 || "",
          key10: fullAsset.key10 || "",
        });
      } else {
        // Reset form for new asset
        form.reset({
          assetCode: "",
          name: "",
          type: AssetType.OTHER,
          supplierId: undefined,
          categoryId: undefined,
          subCategoryId: undefined,
          purchaseDate: "",
          purchasePrice: "",
          purchaseOrderNumber: "",
          maintenanceFrequencyValue: undefined,
          maintenanceFrequency: "",
          lastMaintenanceDate: "",
          maintenanceDueDate: "",
          warrantyExpiryDate: "",
          warrantyPeriod: "",
          lifecycleExpiryDate: "",
          bookValue: "",
          fixedAssetAccountId: undefined,
          depreciationAccountId: undefined,
          expenseAccountId: undefined,
          description: "",
          notes: "",
          status: AssetStatus.AVAILABLE,
          isAllocatedToAllLocations: false,
          // Dynamic fields
          key1: "",
          key2: "",
          key3: "",
          key4: "",
          key5: "",
          key6: "",
          key7: "",
          key8: "",
          key9: "",
          key10: "",
        });
      }
      setUploadedFiles([]);
    }
    setDebouncedName("");
  }, [open, fullAsset, isUpdate, form]);

  // Watch name field for availability checking
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "name" && value.name) {
        const timeoutId = setTimeout(() => {
          setDebouncedName(value.name || "");
        }, 500);
        return () => clearTimeout(timeoutId);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Handle form submission
  const handleSubmit = async (data: AssetFormData) => {
    try {
      setIsSubmitting(true);

      // Convert form data to API format
      const apiData: CreateAssetDto | UpdateAssetDto = {
        assetCode: data.assetCode,
        name: data.name,
        type: data.type,
        supplierId: data.supplierId || undefined,
        categoryId: data.categoryId || undefined,
        subCategoryId: data.subCategoryId || undefined,
        purchaseDate: data.purchaseDate || undefined,
        purchasePrice: data.purchasePrice || undefined,
        purchaseOrderNumber: data.purchaseOrderNumber || undefined,
        maintenanceFrequencyValue: data.maintenanceFrequencyValue || undefined,
        maintenanceFrequency: (data.maintenanceFrequency as any) || undefined,
        lastMaintenanceDate: data.lastMaintenanceDate || undefined,
        maintenanceDueDate: data.maintenanceDueDate || undefined,
        warrantyExpiryDate: data.warrantyExpiryDate || undefined,
        warrantyPeriod: data.warrantyPeriod || undefined,
        lifecycleExpiryDate: data.lifecycleExpiryDate || undefined,
        bookValue: data.bookValue || undefined,
        fixedAssetAccountId: data.fixedAssetAccountId || undefined,
        depreciationAccountId: data.depreciationAccountId || undefined,
        expenseAccountId: data.expenseAccountId || undefined,
        description: data.description || undefined,
        notes: data.notes || undefined,
        status: data.status,
        isAllocatedToAllLocations: data.isAllocatedToAllLocations || false,
        // Dynamic fields
        key1: data.key1 || undefined,
        key2: data.key2 || undefined,
        key3: data.key3 || undefined,
        key4: data.key4 || undefined,
        key5: data.key5 || undefined,
        key6: data.key6 || undefined,
        key7: data.key7 || undefined,
        key8: data.key8 || undefined,
        key9: data.key9 || undefined,
        key10: data.key10 || undefined,
      };

      let response;
      if (isUpdate && asset) {
        response = await updateAssetMutation.mutateAsync({
          id: asset.id,
          data: apiData as UpdateAssetDto,
          image: selectedImage || undefined,
        });
      } else {
        response = await createAssetMutation.mutateAsync({
          data: apiData as CreateAssetDto,
          image: selectedImage || undefined,
        });
      }

      if (response.status === ApiStatus.SUCCESS) {
        toast.success(
          isUpdate ? "Asset updated successfully" : "Asset created successfully"
        );
        onSuccess?.();
        onOpenChange?.(false);
      } else {
        toast.error(response.message || "Something went wrong");
      }
    } catch (error) {
      console.error("Asset submission error:", error);
      toast.error("An error occurred while saving the asset");
    } finally {
      setIsSubmitting(false);
    }
  };

  const errors = form.formState.errors;

  // Check for form errors in different sections
  const hasBasicInfoErrors = !!(errors.assetCode || errors.name || errors.type);
  const hasCategoryErrors = !!(errors.categoryId || errors.subCategoryId);
  const hasPurchaseErrors = !!(
    errors.purchaseDate ||
    errors.purchasePrice ||
    errors.purchaseOrderNumber
  );
  const hasAccountingErrors = !!(
    errors.bookValue ||
    errors.fixedAssetAccountId ||
    errors.depreciationAccountId ||
    errors.expenseAccountId
  );

  // Sections configuration for the form
  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="assetCode">
                  Asset Code <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="assetCode"
                  {...form.register("assetCode")}
                  placeholder="Enter asset code"
                  className={cn(errors.assetCode && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.assetCode && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.assetCode.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="name">
                  Asset Name <span className="text-destructive">*</span>
                  {isCheckingName && (
                    <Loader2 className="inline-block ml-2 h-3 w-3 animate-spin" />
                  )}
                  {!isCheckingName && shouldCheckNameAvailability && (
                    <>
                      {isNameAvailable ? (
                        <Check className="inline-block ml-2 h-3 w-3 text-green-600" />
                      ) : (
                        <X className="inline-block ml-2 h-3 w-3 text-red-600" />
                      )}
                    </>
                  )}
                </Label>
                <Input
                  id="name"
                  {...form.register("name")}
                  placeholder="Enter asset name"
                  className={cn(
                    errors.name && "border-destructive",
                    !isNameAvailable && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.name && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.name.message}
                  </p>
                )}
                {!isNameAvailable && shouldCheckNameAvailability && (
                  <p className="text-sm text-destructive mt-1">
                    This asset name is already taken
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="type">Asset Type</Label>
                <Select
                  value={form.watch("type") || ""}
                  onValueChange={(value) =>
                    form.setValue("type", value as AssetType, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.type && "border-destructive")}
                  >
                    <SelectValue placeholder="Select asset type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(AssetType).map((type) => (
                      <SelectItem key={type} value={type}>
                        {type.replace("_", " ").toLowerCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.type && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.type.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={form.watch("status") || ""}
                  onValueChange={(value) =>
                    form.setValue("status", value as AssetStatus, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(AssetStatus).map((status) => (
                      <SelectItem key={status} value={status}>
                        {status.replace("_", " ").toLowerCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mt-4">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...form.register("description")}
                placeholder="Enter asset description"
                rows={3}
                disabled={isSubmitting}
              />
            </div>
          </AccordionContent>
        </>
      ),
    },

    {
      id: "categories",
      title: "Categories",
      icon: <Tags className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasCategoryErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Tags className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Categories</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Category</Label>
                <Select
                  value={form.watch("categoryId") || ""}
                  onValueChange={(value) =>
                    form.setValue("categoryId", value || undefined, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting || isLoadingCategories}
                >
                  <SelectTrigger
                    className={cn(errors.categoryId && "border-destructive")}
                  >
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.categoryId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.categoryId.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Supplier</Label>
                <Select
                  value={form.watch("supplierId") || ""}
                  onValueChange={(value) =>
                    form.setValue("supplierId", value || undefined, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting || isLoadingSuppliers}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.displayName || supplier.companyName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },

    // Dynamic fields based on asset type
    ...(selectedType && ASSET_TYPE_KEY_MAPPINGS[selectedType]
      ? [
          {
            id: "asset-specifications",
            title: "Asset Specifications",
            icon: <Monitor className="h-5 w-5 text-muted-foreground" />,
            hasErrors: false,
            content: (
              <>
                <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
                  <div className="flex items-center gap-2">
                    <Monitor className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">Asset Specifications</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(ASSET_TYPE_KEY_MAPPINGS[selectedType]).map(
                      ([key, label]) => (
                        <div key={key}>
                          <Label htmlFor={key}>{label}</Label>
                          <Input
                            id={key}
                            {...form.register(key as keyof AssetFormData)}
                            placeholder={`Enter ${label.toLowerCase()}`}
                            disabled={isSubmitting}
                          />
                        </div>
                      )
                    )}
                  </div>
                </AccordionContent>
              </>
            ),
          },
        ]
      : []),

    {
      id: "purchase-details",
      title: "Purchase Details",
      icon: <ShoppingCart className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasPurchaseErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Purchase Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <DatePicker
                  name="purchaseDate"
                  label="Purchase Date"
                  value={form.watch("purchaseDate") || ""}
                  onChange={(date) =>
                    form.setValue(
                      "purchaseDate",
                      date ? date.toISOString().split("T")[0] : "",
                      { shouldValidate: true }
                    )
                  }
                  placeholder="Select purchase date"
                  disabled={isSubmitting}
                  error={errors.purchaseDate?.message}
                />
              </div>

              <div>
                <Label htmlFor="purchasePrice">Purchase Price</Label>
                <Input
                  id="purchasePrice"
                  {...form.register("purchasePrice")}
                  placeholder="Enter purchase price"
                  type="number"
                  step="0.01"
                  className={cn(errors.purchasePrice && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.purchasePrice && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.purchasePrice.message}
                  </p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="purchaseOrderNumber">
                  Purchase Order Number
                </Label>
                <Input
                  id="purchaseOrderNumber"
                  {...form.register("purchaseOrderNumber")}
                  placeholder="Enter purchase order number"
                  disabled={isSubmitting}
                />
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },

    {
      id: "maintenance",
      title: "Maintenance & Warranty",
      icon: <Wrench className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Wrench className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Maintenance & Warranty</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <DatePicker
                  name="lastMaintenanceDate"
                  label="Last Maintenance Date"
                  value={form.watch("lastMaintenanceDate") || ""}
                  onChange={(date) =>
                    form.setValue(
                      "lastMaintenanceDate",
                      date ? date.toISOString().split("T")[0] : "",
                      { shouldValidate: true }
                    )
                  }
                  placeholder="Select last maintenance date"
                  disabled={isSubmitting}
                  error={errors.lastMaintenanceDate?.message}
                />
              </div>

              <div>
                <DatePicker
                  name="maintenanceDueDate"
                  label="Next Maintenance Due"
                  value={form.watch("maintenanceDueDate") || ""}
                  onChange={(date) =>
                    form.setValue(
                      "maintenanceDueDate",
                      date ? date.toISOString().split("T")[0] : "",
                      { shouldValidate: true }
                    )
                  }
                  placeholder="Select maintenance due date"
                  disabled={isSubmitting}
                  error={errors.maintenanceDueDate?.message}
                />
              </div>

              <div>
                <DatePicker
                  name="warrantyExpiryDate"
                  label="Warranty Expiry Date"
                  value={form.watch("warrantyExpiryDate") || ""}
                  onChange={(date) =>
                    form.setValue(
                      "warrantyExpiryDate",
                      date ? date.toISOString().split("T")[0] : "",
                      { shouldValidate: true }
                    )
                  }
                  placeholder="Select warranty expiry date"
                  disabled={isSubmitting}
                  error={errors.warrantyExpiryDate?.message}
                />
              </div>

              <div>
                <Label htmlFor="warrantyPeriod">Warranty Period</Label>
                <Input
                  id="warrantyPeriod"
                  {...form.register("warrantyPeriod")}
                  placeholder="e.g., 2 years"
                  disabled={isSubmitting}
                />
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },

    {
      id: "accounting",
      title: "Accounting",
      icon: <DollarSign className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasAccountingErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Accounting</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="bookValue">Book Value</Label>
                <Input
                  id="bookValue"
                  {...form.register("bookValue")}
                  placeholder="Enter book value"
                  type="number"
                  step="0.01"
                  disabled={isSubmitting}
                />
              </div>

              <div>
                <Label>Fixed Asset Account</Label>
                <Select
                  value={form.watch("fixedAssetAccountId") || ""}
                  onValueChange={(value) =>
                    form.setValue("fixedAssetAccountId", value || undefined, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting || isLoadingAccounts}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select fixed asset account" />
                  </SelectTrigger>
                  <SelectContent>
                    {accounts.map((account) => (
                      <SelectItem key={account.id} value={account.id}>
                        {account.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Depreciation Account</Label>
                <Select
                  value={form.watch("depreciationAccountId") || ""}
                  onValueChange={(value) =>
                    form.setValue("depreciationAccountId", value || undefined, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting || isLoadingAccounts}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select depreciation account" />
                  </SelectTrigger>
                  <SelectContent>
                    {accounts.map((account) => (
                      <SelectItem key={account.id} value={account.id}>
                        {account.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Expense Account</Label>
                <Select
                  value={form.watch("expenseAccountId") || ""}
                  onValueChange={(value) =>
                    form.setValue("expenseAccountId", value || undefined, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting || isLoadingAccounts}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select expense account" />
                  </SelectTrigger>
                  <SelectContent>
                    {accounts.map((account) => (
                      <SelectItem key={account.id} value={account.id}>
                        {account.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },

    {
      id: "asset-image",
      title: "Asset Image",
      icon: <ImageIcon className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Asset Image</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <FileUploader
              files={uploadedFiles}
              onFilesChange={setUploadedFiles}
              fileType="image"
              maxFiles={1}
              maxSize={5 * 1024 * 1024} // 5MB
              multiple={false}
              label="Image"
              description="Upload an image for this asset. Maximum file size is 5MB."
              placeholder="Click to upload or drag and drop"
              disabled={isSubmitting}
              showPreview={true}
              previewSize="md"
              onError={(error) => {
                toast.error(error);
              }}
            />
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<AssetTableData, AssetFormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={asset}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Asset"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
    />
  );
}
