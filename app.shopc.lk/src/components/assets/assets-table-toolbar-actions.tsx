"use client";

import { type Table } from "@tanstack/react-table";
import { AssetSheet } from "./asset-sheet";
import { ImportAssetsSheet } from "./import-assets-sheet";
import { BaseTableToolbarActions } from "@/components/shared/base-table-toolbar-actions";
import { useState } from "react";
import { AssetListDto } from "@/types/asset";

interface AssetsTableToolbarActionsProps {
  table: Table<AssetListDto>;
  onRefresh?: () => Promise<void>;
  rowSelection: boolean;
  onRowSelectionChange: (value: boolean) => void;
  isDemo?: boolean;
}

export function AssetsTableToolbarActions({
  table,
  onRefresh,
  rowSelection,
  onRowSelectionChange,
  isDemo = false,
}: AssetsTableToolbarActionsProps) {
  const [createAssetOpen, setCreateAssetOpen] = useState(false);
  const [importAssetsOpen, setImportAssetsOpen] = useState(false);

  const baseActions = [
    {
      label: "Add Asset",
      onClick: () => setCreateAssetOpen(true),
    },
    {
      label: "Import Assets",
      onClick: () => setImportAssetsOpen(true),
    },
  ];

  const handleAssetSuccess = async () => {
    setCreateAssetOpen(false);
    if (onRefresh) {
      await onRefresh();
    }
  };

  const handleImportSuccess = async () => {
    setImportAssetsOpen(false);
    if (onRefresh) {
      await onRefresh();
    }
  };

  return (
    <>
      <BaseTableToolbarActions
        table={table}
        actions={baseActions}
        rowSelection={rowSelection}
        onRowSelectionChange={onRowSelectionChange}
      />

      <AssetSheet
        open={createAssetOpen}
        onOpenChange={setCreateAssetOpen}
        onSuccess={handleAssetSuccess}
        isDemo={isDemo}
        isUpdate={false}
      />

      <ImportAssetsSheet
        open={importAssetsOpen}
        onOpenChange={setImportAssetsOpen}
        onSuccess={handleImportSuccess}
        isDemo={isDemo}
      />
    </>
  );
}
