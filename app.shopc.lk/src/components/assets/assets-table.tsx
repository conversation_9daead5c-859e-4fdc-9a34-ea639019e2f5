"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { getAssetsTableData } from "@/lib/assets/queries";
import { DeleteAssetsDialog } from "./delete-assets-dialog";
import { AssetsTableToolbarActions } from "./assets-table-toolbar-actions";
import { AssetsTableFloatingBar } from "./assets-table-floating-bar";
import { AssetSheet } from "./asset-sheet";
import { AssetDetails } from "./asset-details";
import { AssetDetailsContent } from "./asset-details-content";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./assets-table-columns";
import { AssetListDto, AssetStatus } from "@/types/asset";
import { useAssetsData } from "@/lib/assets/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { assetKeys } from "@/lib/assets/hooks";

interface AssetsTableProps {
  isDemo?: boolean;
}

export function AssetsTable({ isDemo = false }: AssetsTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<AssetListDto> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const filterFields: DataTableFilterField<AssetListDto>[] = React.useMemo(
    () => [
      {
        id: "name",
        label: "Name",
        placeholder: "Filter by name...",
      },
      {
        id: "assetCode",
        label: "Asset Code",
        placeholder: "Filter by asset code...",
      },
      // status
      {
        id: "status",
        label: "Status",
        placeholder: "Filter by status...",
        type: "select",
        options: [
          { label: "Available", value: AssetStatus.AVAILABLE },
          { label: "Unavailable", value: AssetStatus.UNAVAILABLE },
          { label: "Assigned", value: AssetStatus.ASSIGNED },
          { label: "Discarded", value: AssetStatus.DISCARDED },
          { label: "Lost", value: AssetStatus.LOST },
          { label: "Stolen", value: AssetStatus.STOLEN },
          { label: "Damaged", value: AssetStatus.DAMAGED },
          { label: "Maintenance", value: AssetStatus.MAINTENANCE },
          { label: "Allocated", value: AssetStatus.ALLOCATED },
        ],
      },
    ],
    []
  );

  const advancedFilterFields: DataTableAdvancedFilterField<AssetListDto>[] =
    React.useMemo(
      () => [
        {
          id: "name",
          label: "Name",
          type: "text",
        },
        {
          id: "assetCode",
          label: "Asset Code",
          type: "text",
        },
        {
          id: "status",
          label: "Status",
          type: "select",
          options: [
            { label: "Available", value: AssetStatus.AVAILABLE },
            { label: "Unavailable", value: AssetStatus.UNAVAILABLE },
            { label: "Assigned", value: AssetStatus.ASSIGNED },
            { label: "Discarded", value: AssetStatus.DISCARDED },
            { label: "Lost", value: AssetStatus.LOST },
            { label: "Stolen", value: AssetStatus.STOLEN },
            { label: "Damaged", value: AssetStatus.DAMAGED },
            { label: "Maintenance", value: AssetStatus.MAINTENANCE },
            { label: "Allocated", value: AssetStatus.ALLOCATED },
          ],
        },
      ],
      []
    );

  const sortFields: DataTableAdvancedFilterField<AssetListDto>[] =
    React.useMemo(
      () => [
        {
          id: "name",
          label: "Name",
          type: "text",
        },
        {
          id: "assetCode",
          label: "Asset Code",
          type: "text",
        },
        {
          id: "type",
          label: "Type",
          type: "text",
        },
        {
          id: "status",
          label: "Status",
          type: "text",
        },
        {
          id: "purchaseDate",
          label: "Purchase Date",
          type: "text",
        },
        {
          id: "createdAt",
          label: "Created At",
          type: "text",
        },
      ],
      []
    );

  // Check if there are any active filters
  const hasActiveFilters = React.useMemo(() => {
    const urlParams = new URLSearchParams(window.location.search);
    return Array.from(urlParams.keys()).some((key) =>
      [
        "name",
        "assetCode",
        "status",
        "typeId",
        "locationId",
        "from",
        "to",
        "filters",
      ].includes(key)
    );
  }, []);

  const handleRefresh = React.useCallback(async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: assetKeys.list() }),
      queryClient.invalidateQueries({ queryKey: assetKeys.simple() }),
    ]);
  }, [queryClient]);

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<AssetListDto>) => {
      setRowAction(action);
    },
    []
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1,
      perPage: searchParams.get("perPage")
        ? parseInt(searchParams.get("perPage") as string)
        : 10,
      name: searchParams.get("name") || "",
      assetCode: searchParams.get("assetCode") || "",
      status: searchParams.get("status") || "",
      type: searchParams.get("type") || "",
      categoryId: searchParams.get("categoryId") || "",
      subCategoryId: searchParams.get("subCategoryId") || "",
      locationId: searchParams.get("locationId") || "",
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      filters: (() => {
        const filtersParam = searchParams.get("filters");
        return filtersParam ? JSON.parse(filtersParam) : [];
      })(),
      joinOperator: (searchParams.get("joinOperator") as "and" | "or") || "and",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam ? JSON.parse(sortParam) : [];
      })(),
    };
  }, [searchParams]);

  // Use TanStack Query hook
  const {
    data: assetsData,
    isLoading,
    isFetching,
    isRefetching,
  } = useAssetsData(searchParamsValues, isDemo);

  // Only show full loading indicator for initial loading
  // Show header loading for refetch operations
  const isHeaderLoading = isFetching && !isLoading;

  // Determine if actions should be disabled (during any loading/fetching state)
  const isActionsDisabled = isLoading || isFetching;

  // Render asset details for dialog/drawer
  const renderAssetDetails = React.useCallback(
    (asset: AssetListDto | null) => {
      if (!asset) return null;

      // Convert AssetListDto to AssetTableData for compatibility with existing components
      const assetTableData = {
        id: asset.id,
        assetCode: asset.assetCode,
        name: asset.name,
        type: asset.type,
        status: asset.status,
        categoryId: asset.categoryId,
        subCategoryId: asset.subCategoryId,
        supplierId: asset.supplierId,
        purchaseDate: asset.purchaseDate,
        purchasePrice: asset.purchasePrice,
        createdAt: asset.createdAt,
        updatedAt: asset.updatedAt,
      };

      return <AssetDetailsContent asset={assetTableData} isDemo={isDemo} />;
    },
    [isDemo]
  );

  return (
    <>
      <BaseTable<AssetListDto, Awaited<ReturnType<typeof getAssetsTableData>>>
        data={assetsData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        enableSortableRows={false} // Assets don't have position-based ordering
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.perPage}
        isDragDropEnabled={() => false} // Assets don't support drag and drop
        columns={getColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
          onRefresh: handleRefresh,
          onStatusUpdate: (assetId?: string) => {
            queryClient.invalidateQueries({ queryKey: assetKeys.list() });
            queryClient.invalidateQueries({ queryKey: assetKeys.simple() });
            if (assetId) {
              queryClient.invalidateQueries({
                queryKey: assetKeys.detail(assetId),
              });
            }
          },
        })}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        sortFields={sortFields}
        getInitialData={(response) => {
          return response?.data?.data ?? [];
        }}
        getPageCount={(response) => {
          return response?.data?.meta?.totalPages ?? 1;
        }}
        getRowId={(row) => row.id}
        enableAdvancedTable={false}
        enableFloatingBar={true}
        FloatingBar={(props) => (
          <AssetsTableFloatingBar
            {...props}
            onRefresh={handleRefresh}
            isDemo={isDemo}
          />
        )}
        ToolbarActions={(props) => (
          <AssetsTableToolbarActions {...props} isDemo={isDemo} />
        )}
        onRowAction={handleRowAction}
        defaultSortingId={undefined}
        defaultSortingDesc={false}
        onRefresh={handleRefresh}
        renderDialogContent={renderAssetDetails}
      />

      {/* Asset Sheet for Create/Update */}
      <AssetSheet
        asset={
          rowAction?.type === "update" || rowAction?.type === "view"
            ? // Convert AssetListDto to AssetTableData for compatibility
              {
                id: rowAction.row.original.id,
                assetCode: rowAction.row.original.assetCode,
                name: rowAction.row.original.name,
                type: rowAction.row.original.type,
                status: rowAction.row.original.status,
                categoryId: rowAction.row.original.categoryId,
                subCategoryId: rowAction.row.original.subCategoryId,
                supplierId: rowAction.row.original.supplierId,
                purchaseDate: rowAction.row.original.purchaseDate,
                purchasePrice: rowAction.row.original.purchasePrice,
                createdAt: rowAction.row.original.createdAt,
                updatedAt: rowAction.row.original.updatedAt,
              }
            : null
        }
        open={
          rowAction?.type === "update" || rowAction?.type === "view"
            ? true
            : false
        }
        onOpenChange={(open) => {
          if (!open) {
            setRowAction(null);
          }
        }}
        onSuccess={() => {
          setRowAction(null);
          handleRefresh();
        }}
        isDemo={isDemo}
        isUpdate={rowAction?.type === "update"}
      />

      {/* Asset Details */}
      <AssetDetails
        asset={
          rowAction?.type === "view"
            ? // Convert AssetListDto to AssetTableData for compatibility
              {
                id: rowAction.row.original.id,
                assetCode: rowAction.row.original.assetCode,
                name: rowAction.row.original.name,
                type: rowAction.row.original.type,
                status: rowAction.row.original.status,
                categoryId: rowAction.row.original.categoryId,
                subCategoryId: rowAction.row.original.subCategoryId,
                supplierId: rowAction.row.original.supplierId,
                purchaseDate: rowAction.row.original.purchaseDate,
                purchasePrice: rowAction.row.original.purchasePrice,
                createdAt: rowAction.row.original.createdAt,
                updatedAt: rowAction.row.original.updatedAt,
              }
            : null
        }
        open={rowAction?.type === "view"}
        onOpenChange={(open) => {
          if (!open) {
            setRowAction(null);
          }
        }}
        isDemo={isDemo}
      />

      {/* Delete Assets Dialog */}
      <DeleteAssetsDialog
        assets={
          rowAction?.type === "delete"
            ? [
                {
                  id: rowAction.row.original.id,
                  assetCode: rowAction.row.original.assetCode,
                  name: rowAction.row.original.name,
                  type: rowAction.row.original.type,
                  status: rowAction.row.original.status,
                  categoryId: rowAction.row.original.categoryId,
                  subCategoryId: rowAction.row.original.subCategoryId,
                  supplierId: rowAction.row.original.supplierId,
                  purchaseDate: rowAction.row.original.purchaseDate,
                  purchasePrice: rowAction.row.original.purchasePrice,
                  createdAt: rowAction.row.original.createdAt,
                  updatedAt: rowAction.row.original.updatedAt,
                },
              ]
            : []
        }
        open={rowAction?.type === "delete"}
        onOpenChange={(open) => {
          if (!open) {
            setRowAction(null);
          }
        }}
        onSuccess={() => {
          setRowAction(null);
          handleRefresh();
        }}
        isDemo={isDemo}
      />
    </>
  );
}
