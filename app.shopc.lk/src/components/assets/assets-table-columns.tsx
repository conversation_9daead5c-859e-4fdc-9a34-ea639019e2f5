"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye } from "lucide-react";
import { AssetListDto, AssetStatus, AssetType } from "@/types/asset";
import {
  ASSET_TYPE_KEY_MAPPINGS,
  getAssetFieldLabel,
  formatCurrency,
} from "@/lib/assets/utils";
import { AssetStatusBadge } from "./asset-status-badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "update" | "delete";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (assetId: string, newStatus: AssetStatus) => void;
}

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  onRefresh,
  onStatusUpdate,
}: ColumnsProps) {
  const columns: ColumnDef<AssetListDto>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 10, // Set a fixed small width for the checkbox column
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Asset Name" />
      ),
      cell: ({ row }) => {
        const assetName = row.getValue("name") as string;
        const assetCode = row.original.assetCode;
        const assetType = row.original.type;
        const image = row.original.image;

        return (
          <div className="flex items-center gap-3 pl-2">
            <Avatar className="h-8 w-8">
              {image ? (
                <AvatarImage src={image} alt={assetName} />
              ) : (
                <AvatarFallback className="text-xs">
                  {assetName.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              )}
            </Avatar>
            <div className="flex flex-col">
              <div className="font-medium">{assetName}</div>
              {assetCode && (
                <div className="text-xs text-muted-foreground">{assetCode}</div>
              )}
              <div className="text-xs text-muted-foreground capitalize">
                {assetType?.replace("_", " ").toLowerCase()}
              </div>
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "assetCode",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Asset Code" />
      ),
      cell: ({ row }) => {
        const assetCode = row.getValue("assetCode") as string;
        return (
          <div className="pl-2">
            {assetCode ? (
              <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">
                {assetCode}
              </code>
            ) : (
              <span className="text-xs text-muted-foreground">No code</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "type",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Type" />
      ),
      cell: ({ row }) => {
        const type = row.getValue("type") as AssetType;
        return (
          <div className="pl-2">
            <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
              {type?.replace("_", " ").toLowerCase()}
            </span>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "categoryName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Category" />
      ),
      cell: ({ row }) => {
        const categoryName = row.original.categoryName;
        const subCategoryName = row.original.subCategoryName;
        return (
          <div className="pl-2">
            {categoryName ? (
              <div className="flex flex-col">
                <span className="text-sm font-medium">{categoryName}</span>
                {subCategoryName && (
                  <span className="text-xs text-muted-foreground">
                    {subCategoryName}
                  </span>
                )}
              </div>
            ) : (
              <span className="text-xs text-muted-foreground">No category</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "supplierName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Supplier" />
      ),
      cell: ({ row }) => {
        const supplierName = row.original.supplierName;
        return (
          <div className="pl-2">
            {supplierName ? (
              <span className="text-sm">{supplierName}</span>
            ) : (
              <span className="text-xs text-muted-foreground">No supplier</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "purchasePrice",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Purchase Price" />
      ),
      cell: ({ row }) => {
        const price = row.getValue("purchasePrice") as string;
        return (
          <div className="pl-2">
            {price ? (
              <span className="text-sm font-medium">
                {formatCurrency(price)}
              </span>
            ) : (
              <span className="text-xs text-muted-foreground">No price</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "purchaseDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Purchase Date" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("purchaseDate") as string;
        return (
          <div className="pl-2">
            {date ? (
              <span className="text-sm">
                {new Date(date).toLocaleDateString()}
              </span>
            ) : (
              <span className="text-xs text-muted-foreground">No date</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "locationsCount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Locations" />
      ),
      cell: ({ row }) => {
        const locationsCount = row.original.locationsCount;
        const isAllocatedToAllLocations =
          row.original.isAllocatedToAllLocations;

        return (
          <div className="pl-2">
            {isAllocatedToAllLocations ? (
              <span className="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                All Locations
              </span>
            ) : locationsCount > 0 ? (
              <span className="text-sm font-medium">
                {locationsCount} location{locationsCount !== 1 ? "s" : ""}
              </span>
            ) : (
              <span className="text-xs text-muted-foreground">
                No locations
              </span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "status",
      header: () => <div className="font-medium">Status</div>,
      cell: ({ row }) => {
        const status = row.getValue("status") as AssetStatus;
        return (
          <AssetStatusBadge
            assetId={row.original.id}
            status={status}
            isDemo={isDemo}
            disabled={isActionsDisabled}
            onRefresh={onRefresh}
            onStatusUpdate={onStatusUpdate}
          />
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "actions",
      header: () => <div className="font-medium">Actions</div>,
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                disabled={isActionsDisabled}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "view",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
                <DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "update",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Edit
                <DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "delete",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Delete
                <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return columns;
}
