"use client";

import * as React from "react";
import {
  Info,
  Image as ImageIcon,
  Calendar,
  User,
  Tag,
  Hash,
  FileText,
  Printer,
  Download,
  Package,
  Settings,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useAsset } from "@/lib/assets/hooks";
import { AssetTableData, AssetStatus, AssetType } from "@/types/asset";
import {
  ASSET_TYPE_KEY_MAPPINGS,
  getAssetFieldLabel,
  formatCurrency,
  formatAssetDate,
} from "@/lib/assets/utils";
import { LoadingIndicator } from "@/components/shared/loading-indicator";
import { toast } from "sonner";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import Image from "next/image";

interface AssetDetailsContentProps {
  asset: AssetTableData;
  isDemo?: boolean;
}

export function AssetDetailsContent({
  asset,
  isDemo = false,
}: AssetDetailsContentProps) {
  const printRef = React.useRef<HTMLDivElement>(null);

  // Fetch complete asset data
  const { data: fullAssetResponse, isLoading: isLoadingAsset } = useAsset(
    asset.id,
    isDemo
  );
  const fullAsset = fullAssetResponse?.data;

  // Print functionality
  const handlePrint = React.useCallback(() => {
    const printWindow = window.open("", "_blank");
    if (!printWindow || !printRef.current) return;

    const printContent = printRef.current.cloneNode(true) as HTMLElement;

    // Create a complete HTML document for printing
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Asset Details - ${fullAsset?.name || "Asset"}</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
            .print-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .print-header h1 { margin: 0; font-size: 24px; color: #333; }
            .print-header p { margin: 5px 0 0 0; color: #666; }
            .section { margin-bottom: 25px; page-break-inside: avoid; }
            .section h3 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-bottom: 15px; }
            .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
            .field { margin-bottom: 10px; }
            .field label { font-weight: bold; color: #555; display: block; margin-bottom: 3px; font-size: 12px; }
            .field div { color: #333; }
            .badge { display: inline-block; padding: 2px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; }
            .badge.available { background-color: #d4edda; color: #155724; }
            .badge.unavailable { background-color: #f8d7da; color: #721c24; }
            .badge.maintenance { background-color: #fff3cd; color: #856404; }
            .badge.assigned { background-color: #d1ecf1; color: #0c5460; }
            .image-container { margin-top: 10px; }
            .image-container img { max-width: 200px; height: 120px; object-fit: cover; border: 1px solid #ddd; }
            @media print { 
              body { margin: 0; }
              .no-print { display: none !important; }
              .page-break { page-break-before: always; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <h1>Asset Details</h1>
            <p>Generated on ${new Date().toLocaleDateString()}</p>
          </div>
          ${printContent.innerHTML}
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }, [fullAsset?.name]);

  // Export to PDF functionality
  const handleExportToPDF = React.useCallback(async () => {
    if (!printRef.current || !fullAsset) return;

    try {
      const canvas = await html2canvas(printRef.current, {
        scale: 2,
        useCORS: true,
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF("p", "mm", "a4");
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`asset-${fullAsset.assetCode || fullAsset.name}.pdf`);
      toast.success("PDF exported successfully");
    } catch (error) {
      console.error("Error exporting PDF:", error);
      toast.error("Failed to export PDF");
    }
  }, [fullAsset]);

  if (isLoadingAsset) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingIndicator />
      </div>
    );
  }

  if (!fullAsset) {
    return (
      <div className="flex items-center justify-center p-8 text-muted-foreground">
        Asset not found
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex items-center justify-between border-b pb-4 no-print">
        <div>
          <h2 className="text-2xl font-bold">{fullAsset.name}</h2>
          <p className="text-muted-foreground">
            {fullAsset.assetCode && (
              <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">
                {fullAsset.assetCode}
              </code>
            )}
            {fullAsset.type && (
              <span className="ml-2 inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                {fullAsset.type.replace("_", " ").toLowerCase()}
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrint}
            className="no-print"
          >
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportToPDF}
            className="no-print"
          >
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Printable Content */}
      <div ref={printRef} className="space-y-6">
        {/* Basic Information */}
        <div className="section">
          <h3 className="flex items-center gap-2 text-lg font-semibold mb-4">
            <Info className="h-5 w-5 text-muted-foreground" />
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="field">
              <label>Asset Name</label>
              <div className="font-medium">{fullAsset.name}</div>
            </div>

            {fullAsset.assetCode && (
              <div className="field">
                <label>Asset Code</label>
                <div>
                  <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">
                    {fullAsset.assetCode}
                  </code>
                </div>
              </div>
            )}

            {fullAsset.type && (
              <div className="field">
                <label>Type</label>
                <div>
                  <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                    {fullAsset.type.replace("_", " ").toLowerCase()}
                  </span>
                </div>
              </div>
            )}

            <div className="field">
              <label>Status</label>
              <div>
                <Badge
                  variant={
                    fullAsset.status === AssetStatus.AVAILABLE
                      ? "default"
                      : "secondary"
                  }
                  className={cn(
                    fullAsset.status === AssetStatus.AVAILABLE &&
                      "bg-green-100 text-green-800",
                    fullAsset.status === AssetStatus.UNAVAILABLE &&
                      "bg-red-100 text-red-800",
                    fullAsset.status === AssetStatus.MAINTENANCE &&
                      "bg-yellow-100 text-yellow-800",
                    fullAsset.status === AssetStatus.ASSIGNED &&
                      "bg-blue-100 text-blue-800"
                  )}
                >
                  {fullAsset.status.replace("_", " ").toLowerCase()}
                </Badge>
              </div>
            </div>

            {fullAsset.description && (
              <div className="field md:col-span-2">
                <label>Description</label>
                <div>{fullAsset.description}</div>
              </div>
            )}
          </div>
        </div>

        {/* Financial Information */}
        <div className="section">
          <h3 className="flex items-center gap-2 text-lg font-semibold mb-4">
            <Package className="h-5 w-5 text-muted-foreground" />
            Financial Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {fullAsset.purchasePrice && (
              <div className="field">
                <label>Purchase Price</label>
                <div className="font-medium">
                  {formatCurrency(fullAsset.purchasePrice)}
                </div>
              </div>
            )}

            {fullAsset.purchaseDate && (
              <div className="field">
                <label>Purchase Date</label>
                <div>{formatAssetDate(fullAsset.purchaseDate)}</div>
              </div>
            )}

            {fullAsset.bookValue && (
              <div className="field">
                <label>Book Value</label>
                <div className="font-medium">
                  {formatCurrency(fullAsset.bookValue)}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Dynamic Asset Fields */}
        {fullAsset.type && ASSET_TYPE_KEY_MAPPINGS[fullAsset.type] && (
          <div className="section">
            <h3 className="flex items-center gap-2 text-lg font-semibold mb-4">
              <Settings className="h-5 w-5 text-muted-foreground" />
              Asset Specifications
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(ASSET_TYPE_KEY_MAPPINGS[fullAsset.type]).map(
                ([key, label]) => {
                  const value = fullAsset[
                    key as keyof typeof fullAsset
                  ] as string;
                  if (!value) return null;

                  return (
                    <div key={key} className="field">
                      <label>{label}</label>
                      <div>{value}</div>
                    </div>
                  );
                }
              )}
            </div>
          </div>
        )}

        {/* Maintenance & Warranty */}
        {(fullAsset.lastMaintenanceDate ||
          fullAsset.maintenanceDueDate ||
          fullAsset.warrantyExpiryDate ||
          fullAsset.warrantyPeriod) && (
          <div className="section">
            <h3 className="flex items-center gap-2 text-lg font-semibold mb-4">
              <Settings className="h-5 w-5 text-muted-foreground" />
              Maintenance & Warranty
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {fullAsset.lastMaintenanceDate && (
                <div className="field">
                  <label>Last Maintenance Date</label>
                  <div>{formatAssetDate(fullAsset.lastMaintenanceDate)}</div>
                </div>
              )}

              {fullAsset.maintenanceDueDate && (
                <div className="field">
                  <label>Next Maintenance Due</label>
                  <div>{formatAssetDate(fullAsset.maintenanceDueDate)}</div>
                </div>
              )}

              {fullAsset.warrantyExpiryDate && (
                <div className="field">
                  <label>Warranty Expiry Date</label>
                  <div>{formatAssetDate(fullAsset.warrantyExpiryDate)}</div>
                </div>
              )}

              {fullAsset.warrantyPeriod && (
                <div className="field">
                  <label>Warranty Period</label>
                  <div>{fullAsset.warrantyPeriod}</div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Additional Notes */}
        {fullAsset.notes && (
          <div className="section">
            <h3 className="flex items-center gap-2 text-lg font-semibold mb-4">
              <FileText className="h-5 w-5 text-muted-foreground" />
              Additional Notes
            </h3>
            <div className="p-4 bg-muted rounded-lg">
              <p className="text-sm whitespace-pre-wrap">{fullAsset.notes}</p>
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="section">
          <h3 className="flex items-center gap-2 text-lg font-semibold mb-4">
            <Calendar className="h-5 w-5 text-muted-foreground" />
            Timestamps
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="field">
              <label>Created At</label>
              <div>{formatAssetDate(fullAsset.createdAt)}</div>
            </div>

            <div className="field">
              <label>Last Updated</label>
              <div>{formatAssetDate(fullAsset.updatedAt)}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
