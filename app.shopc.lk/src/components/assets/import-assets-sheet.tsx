/**
 * ImportAssetsSheet Component
 *
 * This component allows importing assets with the following features:
 * - Manual data entry or file upload (CSV/Excel)
 * - Support for asset images with preview
 * - Field validation and error handling
 * - Integration with bulk import API with images
 *
 * Image handling:
 * - Images are stored in uploadedImages array
 * - Each asset row stores imageIndex to reference the image
 * - Images are displayed with filename and small preview
 * - Supports selective image assignment (some assets can have images, others don't)
 */

"use client";

import { useState, useMemo, useEffect, useCallback } from "react";
import {
  ImportDataSheet,
  FieldMapping,
  FieldConfig,
} from "../data-import/import-data-sheet";
import { createAssetSchema, GetAssetsSchema } from "@/lib/assets/validations";
import { AssetStatus, AssetType } from "@/types/asset";
import { ApiStatus } from "@/types/common";
import {
  useAssetsData,
  useAssetNameAvailability,
  useBulkImportAssetsWithImages,
} from "@/lib/assets/hooks";

// Define the asset field types that can be imported
export type AssetImportFields =
  | "assetCode"
  | "name"
  | "type"
  | "supplierId"
  | "categoryId"
  | "subCategoryId"
  | "purchaseDate"
  | "purchasePrice"
  | "purchaseOrderNumber"
  | "maintenanceFrequencyValue"
  | "maintenanceFrequency"
  | "lastMaintenanceDate"
  | "maintenanceDueDate"
  | "warrantyExpiryDate"
  | "warrantyPeriod"
  | "lifecycleExpiryDate"
  | "bookValue"
  | "fixedAssetAccountId"
  | "depreciationAccountId"
  | "expenseAccountId"
  | "description"
  | "notes"
  | "status"
  | "isAllocatedToAllLocations"
  // Dynamic fields
  | "key1"
  | "key2"
  | "key3"
  | "key4"
  | "key5"
  | "key6"
  | "key7"
  | "key8"
  | "key9"
  | "key10"
  | "image";

// Required fields for asset import
const REQUIRED_ASSET_FIELDS: AssetImportFields[] = ["assetCode", "name"];

// All possible fields for asset import
const ALL_ASSET_FIELDS: AssetImportFields[] = [
  "assetCode",
  "name",
  "type",
  "supplierId",
  "categoryId",
  "subCategoryId",
  "purchaseDate",
  "purchasePrice",
  "purchaseOrderNumber",
  "maintenanceFrequencyValue",
  "maintenanceFrequency",
  "lastMaintenanceDate",
  "maintenanceDueDate",
  "warrantyExpiryDate",
  "warrantyPeriod",
  "lifecycleExpiryDate",
  "bookValue",
  "fixedAssetAccountId",
  "depreciationAccountId",
  "expenseAccountId",
  "description",
  "notes",
  "status",
  "isAllocatedToAllLocations",
  // Dynamic fields
  "key1",
  "key2",
  "key3",
  "key4",
  "key5",
  "key6",
  "key7",
  "key8",
  "key9",
  "key10",
  "image",
];

export type ImportAssetsSheetProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isDemo?: boolean;
};

export function ImportAssetsSheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: ImportAssetsSheetProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hook for bulk importing assets with images
  const bulkImportMutation = useBulkImportAssetsWithImages(isDemo);

  // Fetch full assets data for availability checking (includes all fields)
  const { data: fullAssetsResponse } = useAssetsData(
    {
      page: 1,
      perPage: 1000, // Get all assets for availability checking
    } as GetAssetsSchema,
    isDemo
  );
  const fullAssets = fullAssetsResponse?.data?.data || [];

  // Track validation state for availability checking
  const [currentValidatingName, setCurrentValidatingName] =
    useState<string>("");

  // Debounced availability checking to prevent infinite loops
  const debouncedCheckAvailability = useCallback(
    (field: string, value: string) => {
      if (!value || !value.trim()) return;

      const trimmedValue = value.trim();

      // Only trigger if not already checking this value
      setTimeout(() => {
        switch (field) {
          case "name":
            if (currentValidatingName !== trimmedValue) {
              setCurrentValidatingName(trimmedValue);
            }
            break;
        }
      }, 300); // 300ms debounce
    },
    [currentValidatingName]
  );

  // Use availability hooks for current validating values
  const { data: nameAvailability, isLoading: isLoadingNameCheck } =
    useAssetNameAvailability(currentValidatingName, isDemo);

  // Build a cache of checked availability
  const [availabilityCache, setAvailabilityCache] = useState<{
    names: Map<string, boolean>;
    assetCodes: Map<string, boolean>;
  }>({
    names: new Map(),
    assetCodes: new Map(),
  });

  // Update availability cache when data comes in
  useEffect(() => {
    if (
      currentValidatingName &&
      nameAvailability?.data?.available !== undefined
    ) {
      setAvailabilityCache((prev) => ({
        ...prev,
        names: new Map(prev.names).set(
          currentValidatingName,
          nameAvailability.data!.available
        ),
      }));
    }
  }, [currentValidatingName, nameAvailability]);

  // Validation functions
  const validateField = useCallback(
    (field: string, value: string, _rowIndex: number): string | null => {
      if (!value || !value.trim()) {
        return REQUIRED_ASSET_FIELDS.includes(field as AssetImportFields)
          ? `${field} is required`
          : null;
      }

      const trimmedValue = value.trim();

      switch (field) {
        case "assetCode":
          if (trimmedValue.length < 2) {
            return "Asset code must be at least 2 characters";
          }
          // Check for duplicates within the import data
          const duplicateAssetCodes = fullAssets.filter(
            (asset) =>
              asset.assetCode.toLowerCase() === trimmedValue.toLowerCase()
          );
          if (duplicateAssetCodes.length > 0) {
            return "Asset code already exists";
          }
          break;

        case "name":
          if (trimmedValue.length < 2) {
            return "Asset name must be at least 2 characters";
          }
          // Check cache first
          const cachedNameAvailability =
            availabilityCache.names.get(trimmedValue);
          if (cachedNameAvailability === false) {
            return "Asset name already exists";
          }
          break;

        case "purchasePrice":
        case "bookValue":
          if (trimmedValue && isNaN(parseFloat(trimmedValue))) {
            return "Must be a valid number";
          }
          break;

        case "maintenanceFrequencyValue":
          if (
            trimmedValue &&
            (isNaN(parseInt(trimmedValue)) || parseInt(trimmedValue) < 1)
          ) {
            return "Must be a positive number";
          }
          break;

        case "purchaseDate":
        case "lastMaintenanceDate":
        case "maintenanceDueDate":
        case "warrantyExpiryDate":
        case "lifecycleExpiryDate":
          if (trimmedValue && isNaN(Date.parse(trimmedValue))) {
            return "Must be a valid date";
          }
          break;

        case "status":
          if (
            trimmedValue &&
            !Object.values(AssetStatus).includes(trimmedValue as AssetStatus)
          ) {
            return `Must be one of: ${Object.values(AssetStatus).join(", ")}`;
          }
          break;

        case "type":
          if (
            trimmedValue &&
            !Object.values(AssetType).includes(trimmedValue as AssetType)
          ) {
            return `Must be one of: ${Object.values(AssetType).join(", ")}`;
          }
          break;

        case "isAllocatedToAllLocations":
          if (
            trimmedValue &&
            !["true", "false", "yes", "no", "1", "0"].includes(
              trimmedValue.toLowerCase()
            )
          ) {
            return "Must be true/false, yes/no, or 1/0";
          }
          break;
      }

      return null;
    },
    [availabilityCache, fullAssets]
  );

  // Check if field is currently loading
  const isFieldLoading = useCallback(
    (field: string, value: string): boolean => {
      if (!value || !value.trim()) return false;

      switch (field) {
        case "name":
          return isLoadingNameCheck && currentValidatingName === value.trim();
        default:
          return false;
      }
    },
    [isLoadingNameCheck, currentValidatingName]
  );

  // Field configurations for assets with new structure
  const ASSET_FIELD_CONFIGS: FieldConfig[] = [
    {
      name: "assetCode",
      type: "text",
      defaultValue: "",
      placeholder: "Enter asset code (e.g., AST001, COMP-001)",
    },
    {
      name: "name",
      type: "text",
      defaultValue: "",
      placeholder: "Enter asset name (e.g., Laptop, Desk, Printer)",
      isLoading: (value: string) => isFieldLoading("name", value),
      onValueChange: (value: string) =>
        debouncedCheckAvailability("name", value),
    },
    {
      name: "type",
      type: "select",
      options: Object.values(AssetType).map((type) => ({
        value: type,
        label: type.replace("_", " ").toLowerCase(),
      })),
      defaultValue: AssetType.OTHER,
    },
    {
      name: "supplierId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter supplier ID (optional)",
    },
    {
      name: "categoryId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter category ID (optional)",
    },
    {
      name: "subCategoryId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter sub-category ID (optional)",
    },
    {
      name: "purchaseDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter purchase date (YYYY-MM-DD)",
    },
    {
      name: "purchasePrice",
      type: "text",
      defaultValue: "",
      placeholder: "Enter purchase price (optional)",
    },
    {
      name: "purchaseOrderNumber",
      type: "text",
      defaultValue: "",
      placeholder: "Enter purchase order number (optional)",
    },
    {
      name: "maintenanceFrequencyValue",
      type: "text",
      defaultValue: "",
      placeholder: "Enter maintenance frequency value (optional)",
    },
    {
      name: "maintenanceFrequency",
      type: "select",
      options: [
        { value: "days", label: "Days" },
        { value: "weeks", label: "Weeks" },
        { value: "months", label: "Months" },
        { value: "years", label: "Years" },
      ],
      defaultValue: "",
    },
    {
      name: "lastMaintenanceDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter last maintenance date (YYYY-MM-DD)",
    },
    {
      name: "maintenanceDueDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter maintenance due date (YYYY-MM-DD)",
    },
    {
      name: "warrantyExpiryDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter warranty expiry date (YYYY-MM-DD)",
    },
    {
      name: "warrantyPeriod",
      type: "text",
      defaultValue: "",
      placeholder: "Enter warranty period (e.g., 2 years)",
    },
    {
      name: "lifecycleExpiryDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter lifecycle expiry date (YYYY-MM-DD)",
    },
    {
      name: "bookValue",
      type: "text",
      defaultValue: "",
      placeholder: "Enter book value (optional)",
    },
    {
      name: "fixedAssetAccountId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter fixed asset account ID (optional)",
    },
    {
      name: "depreciationAccountId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter depreciation account ID (optional)",
    },
    {
      name: "expenseAccountId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter expense account ID (optional)",
    },
    {
      name: "description",
      type: "text",
      defaultValue: "",
      placeholder: "Enter asset description (optional)",
    },
    {
      name: "notes",
      type: "text",
      defaultValue: "",
      placeholder: "Enter additional notes (optional)",
    },
    {
      name: "status",
      type: "select",
      options: Object.values(AssetStatus).map((status) => ({
        value: status,
        label: status.replace("_", " ").toLowerCase(),
      })),
      defaultValue: AssetStatus.AVAILABLE,
    },
    {
      name: "isAllocatedToAllLocations",
      type: "select",
      options: [
        { value: "false", label: "No" },
        { value: "true", label: "Yes" },
      ],
      defaultValue: "false",
    },
    // Dynamic fields
    {
      name: "key1",
      type: "text",
      defaultValue: "",
      placeholder: "Enter key1 value (asset type specific)",
    },
    {
      name: "key2",
      type: "text",
      defaultValue: "",
      placeholder: "Enter key2 value (asset type specific)",
    },
    {
      name: "key3",
      type: "text",
      defaultValue: "",
      placeholder: "Enter key3 value (asset type specific)",
    },
    {
      name: "key4",
      type: "text",
      defaultValue: "",
      placeholder: "Enter key4 value (asset type specific)",
    },
    {
      name: "key5",
      type: "text",
      defaultValue: "",
      placeholder: "Enter key5 value (asset type specific)",
    },
    {
      name: "key6",
      type: "text",
      defaultValue: "",
      placeholder: "Enter key6 value (asset type specific)",
    },
    {
      name: "key7",
      type: "text",
      defaultValue: "",
      placeholder: "Enter key7 value (asset type specific)",
    },
    {
      name: "key8",
      type: "text",
      defaultValue: "",
      placeholder: "Enter key8 value (asset type specific)",
    },
    {
      name: "key9",
      type: "text",
      defaultValue: "",
      placeholder: "Enter key9 value (asset type specific)",
    },
    {
      name: "key10",
      type: "text",
      defaultValue: "",
      placeholder: "Enter key10 value (asset type specific)",
    },
    {
      name: "image",
      type: "image",
      defaultValue: "",
      placeholder: "Select asset image",
      accept: "image/*",
    },
  ];

  // Default field mappings (can be customized by user)
  const defaultFieldMappings: FieldMapping[] = [
    { sourceField: "Asset Code", targetField: "assetCode" },
    { sourceField: "Asset Name", targetField: "name" },
    { sourceField: "Type", targetField: "type" },
    { sourceField: "Status", targetField: "status" },
    { sourceField: "Purchase Date", targetField: "purchaseDate" },
    { sourceField: "Purchase Price", targetField: "purchasePrice" },
    { sourceField: "Description", targetField: "description" },
  ];

  // Handle successful import
  const handleImportSuccess = useCallback(
    (importedCount: number) => {
      onSuccess();
      onOpenChange(false);
    },
    [onSuccess, onOpenChange]
  );

  // Handle import submission
  const handleImportSubmit = useCallback(
    async (data: any[], images: File[]) => {
      setIsSubmitting(true);

      try {
        // Convert boolean fields
        const processedData = data.map((item) => ({
          ...item,
          isAllocatedToAllLocations:
            item.isAllocatedToAllLocations === "true" ||
            item.isAllocatedToAllLocations === "yes" ||
            item.isAllocatedToAllLocations === "1",
          maintenanceFrequencyValue: item.maintenanceFrequencyValue
            ? parseInt(item.maintenanceFrequencyValue)
            : undefined,
        }));

        const result = await bulkImportMutation.mutateAsync({
          assets: processedData,
          images,
        });

        if (result.status === ApiStatus.SUCCESS) {
          handleImportSuccess(processedData.length);
        } else {
          throw new Error(result.message || "Import failed");
        }
      } catch (error) {
        console.error("Import error:", error);
        throw error;
      } finally {
        setIsSubmitting(false);
      }
    },
    [bulkImportMutation, handleImportSuccess]
  );

  return (
    <ImportDataSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Import Assets"
      description="Import assets from CSV file or enter data manually. You can also upload images for each asset."
      fieldConfigs={ASSET_FIELD_CONFIGS}
      defaultFieldMappings={defaultFieldMappings}
      requiredFields={REQUIRED_ASSET_FIELDS}
      allFields={ALL_ASSET_FIELDS}
      validateField={validateField}
      onSubmit={handleImportSubmit}
      isSubmitting={isSubmitting}
      enableImages={true}
      imageFieldName="image"
      maxImageSize={5 * 1024 * 1024} // 5MB
      supportedImageTypes={["image/jpeg", "image/png", "image/webp"]}
    />
  );
}
