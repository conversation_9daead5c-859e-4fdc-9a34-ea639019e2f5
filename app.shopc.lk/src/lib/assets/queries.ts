import {
  AssetIdResponse,
  BulkAssetIdsResponse,
  BulkDeleteAssetResponse,
  AssetNameAvailabilityResponse,
  SimpleAssetResponse,
  AssetResponse,
  CreateAssetDto,
  UpdateAssetDto,
  BulkCreateAssetDto,
  AssetStatus,
  BulkUpdateAssetStatusDto,
  BulkUpdateAssetStatusResponse,
  AssetTableData,
  PaginatedAssetsOptimizedResponseDto,
} from "@/types/asset";
import { ApiResponse, ApiStatus } from "@/types/common";
import {
  GetAssetsSchema,
  CreateAssetSchema,
  UpdateAssetSchema,
  BulkCreateAssetsSchema,
  ImportAssetSchema,
  BulkImportAssetsWithImagesSchema,
} from "./validations";

// Real API imports
import {
  createAssetApi,
  bulkCreateAssetsApi,
  getAssetsApi,
  checkAssetNameAvailabilityApi,
  getAssetsSlimApi,
  getAssetApi,
  updateAssetApi,
  deleteAssetApi,
  bulkDeleteAssetsApi,
  updateAssetStatusApi,
  bulkUpdateAssetStatusApi,
} from "./api";

// Demo API imports
import {
  getDemoAssetsApi,
  getDemoAssetsSlimApi,
  getDemoAssetApi,
  checkDemoAssetNameAvailabilityApi,
  createDemoAssetApi,
  bulkCreateDemoAssetsApi,
  updateDemoAssetApi,
  deleteDemoAssetApi,
  bulkDeleteDemoAssetsApi,
  bulkUpdateDemoAssetStatusApi,
} from "./demo";

// Get assets table data with pagination and filters
export async function getAssetsTableData(
  params: GetAssetsSchema,
  isDemo: boolean
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  if (isDemo) {
    return await getDemoAssetsApi(params);
  } else {
    return await getAssetsApi(params);
  }
}

// Get assets in slim format (for dropdowns/selects)
export async function getAssetsSlim(
  isDemo: boolean
): Promise<SimpleAssetResponse> {
  if (isDemo) {
    return await getDemoAssetsSlimApi();
  } else {
    return await getAssetsSlimApi();
  }
}

// Get a single asset by ID
export async function getAsset(
  id: string,
  isDemo: boolean
): Promise<AssetResponse> {
  if (isDemo) {
    return await getDemoAssetApi(id);
  } else {
    return await getAssetApi(id);
  }
}

// Check asset name availability
export async function checkAssetNameAvailability(
  name: string,
  isDemo: boolean,
  excludeId?: string
): Promise<AssetNameAvailabilityResponse> {
  if (isDemo) {
    return await checkDemoAssetNameAvailabilityApi(name, excludeId);
  } else {
    return await checkAssetNameAvailabilityApi({ name, excludeId });
  }
}

// Create a new asset
export async function createAsset(
  data: CreateAssetDto,
  image?: File,
  isDemo: boolean = false
): Promise<AssetIdResponse> {
  if (isDemo) {
    return await createDemoAssetApi(data, image);
  } else {
    return await createAssetApi(data, image);
  }
}

// Bulk create assets
export async function bulkCreateAssets(
  assets: BulkCreateAssetDto[],
  images?: File[],
  isDemo: boolean = false
): Promise<BulkAssetIdsResponse> {
  if (isDemo) {
    return await bulkCreateDemoAssetsApi(assets, images);
  } else {
    return await bulkCreateAssetsApi(assets, images);
  }
}

// Update an asset
export async function updateAsset(
  id: string,
  data: UpdateAssetDto,
  image?: File,
  isDemo: boolean = false
): Promise<AssetIdResponse> {
  if (isDemo) {
    return await updateDemoAssetApi(id, data, image);
  } else {
    return await updateAssetApi(id, data, image);
  }
}

// Delete an asset
export async function deleteAsset(
  id: string,
  isDemo: boolean = false
): Promise<ApiResponse<{ id: string; message: string }>> {
  if (isDemo) {
    return await deleteDemoAssetApi(id);
  } else {
    return await deleteAssetApi(id);
  }
}

// Bulk delete assets
export async function bulkDeleteAssets(
  assetIds: string[],
  isDemo: boolean = false
): Promise<BulkDeleteAssetResponse> {
  if (isDemo) {
    return await bulkDeleteDemoAssetsApi(assetIds);
  } else {
    return await bulkDeleteAssetsApi(assetIds);
  }
}

// Update asset status
export async function updateAssetStatus(
  id: string,
  status: AssetStatus,
  isDemo: boolean = false
): Promise<AssetIdResponse> {
  if (isDemo) {
    // For demo, we'll use the update function with status
    return await updateDemoAssetApi(id, { status });
  } else {
    return await updateAssetStatusApi(id, status);
  }
}

// Bulk update asset status
export async function bulkUpdateAssetStatus(
  data: BulkUpdateAssetStatusDto,
  isDemo: boolean = false
): Promise<BulkUpdateAssetStatusResponse> {
  if (isDemo) {
    return await bulkUpdateDemoAssetStatusApi(data);
  } else {
    return await bulkUpdateAssetStatusApi(data);
  }
}

// Bulk import assets with images
export async function bulkImportAssetsWithImages(
  data: BulkImportAssetsWithImagesSchema,
  isDemo: boolean = false
): Promise<BulkAssetIdsResponse> {
  if (isDemo) {
    return await bulkCreateDemoAssetsApi(data.assets, data.images);
  } else {
    return await bulkCreateAssetsApi(data.assets, data.images);
  }
}

// Utility functions for asset management

// Get assets by status
export async function getAssetsByStatus(
  status: AssetStatus,
  isDemo: boolean = false
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  const params: GetAssetsSchema = {
    page: 1,
    perPage: 100,
    status,
    filters: [],
    joinOperator: "and",
    sort: [{ id: "createdAt", desc: true }],
  };

  return await getAssetsTableData(params, isDemo);
}

// Get available assets
export async function getAvailableAssets(
  isDemo: boolean = false
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  return await getAssetsByStatus(AssetStatus.AVAILABLE, isDemo);
}

// Get assigned assets
export async function getAssignedAssets(
  isDemo: boolean = false
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  return await getAssetsByStatus(AssetStatus.ASSIGNED, isDemo);
}

// Get assets in maintenance
export async function getMaintenanceAssets(
  isDemo: boolean = false
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  return await getAssetsByStatus(AssetStatus.MAINTENANCE, isDemo);
}

// Search assets by name or asset code
export async function searchAssets(
  query: string,
  isDemo: boolean = false
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  const params: GetAssetsSchema = {
    page: 1,
    perPage: 50,
    name: query,
    filters: [],
    joinOperator: "and",
    sort: [{ id: "name", desc: false }],
  };

  return await getAssetsTableData(params, isDemo);
}

// Get assets by type
export async function getAssetsByType(
  type: string,
  isDemo: boolean = false
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  const params: GetAssetsSchema = {
    page: 1,
    perPage: 100,
    type, // Changed from typeId to type
    filters: [],
    joinOperator: "and",
    sort: [{ id: "createdAt", desc: true }],
  };

  return await getAssetsTableData(params, isDemo);
}

// Get assets by location
export async function getAssetsByLocation(
  locationId: string,
  isDemo: boolean = false
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  const params: GetAssetsSchema = {
    page: 1,
    perPage: 100,
    locationId,
    filters: [],
    joinOperator: "and",
    sort: [{ id: "createdAt", desc: true }],
  };

  return await getAssetsTableData(params, isDemo);
}

// Get recent assets (created in the last 30 days)
export async function getRecentAssets(
  isDemo: boolean = false
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const params: GetAssetsSchema = {
    page: 1,
    perPage: 50,
    from: thirtyDaysAgo.toISOString().split("T")[0],
    filters: [],
    joinOperator: "and",
    sort: [{ id: "createdAt", desc: true }],
  };

  return await getAssetsTableData(params, isDemo);
}
