import { z } from "zod";
import { AssetStatus, AssetType, MaintenanceFrequency, AssetReference } from "@/types/asset";

// Backend DTO: CreateAssetDto - Updated to match new schema
export const createAssetSchema = z.object({
  assetCode: z.string().min(1, "Asset code is required"),
  name: z.string().min(1, "Name is required"),
  type: z.enum([
    AssetType.IT_EQUIPMENT,
    AssetType.FURNITURE,
    AssetType.VEHICLE,
    AssetType.MACHINERY,
    AssetType.BUILDING,
    AssetType.OFFICE_EQUIPMENT,
    AssetType.SOFTWARE,
    AssetType.OTHER,
  ]).optional().default(AssetType.OTHER),
  
  // Supplier Details
  supplierId: z.string().uuid("Invalid supplier ID").optional(),
  
  purchaseDate: z.string().optional(),
  purchasePrice: z.string().optional(),
  purchaseOrderNumber: z.string().optional(),
  
  // Maintenance Details
  maintenanceFrequencyValue: z.number().min(1).optional(),
  maintenanceFrequency: z.enum([
    MaintenanceFrequency.DAYS,
    MaintenanceFrequency.WEEKS,
    MaintenanceFrequency.MONTHS,
    MaintenanceFrequency.YEARS,
  ]).optional(),
  lastMaintenanceDate: z.string().optional(),
  maintenanceDueDate: z.string().optional(),
  warrantyExpiryDate: z.string().optional(),
  warrantyPeriod: z.string().optional(),
  lifecycleExpiryDate: z.string().optional(),
  
  // Dynamic fields based on asset type
  key1: z.string().optional(),
  key2: z.string().optional(),
  key3: z.string().optional(),
  key4: z.string().optional(),
  key5: z.string().optional(),
  key6: z.string().optional(),
  key7: z.string().optional(),
  key8: z.string().optional(),
  key9: z.string().optional(),
  key10: z.string().optional(),
  
  categoryId: z.string().uuid("Invalid category ID").optional(),
  subCategoryId: z.string().uuid("Invalid sub-category ID").optional(),
  isAllocatedToAllLocations: z.boolean().optional().default(false),
  bookValue: z.string().optional(),
  
  // Accounting Accounts
  fixedAssetAccountId: z.string().uuid("Invalid fixed asset account ID").optional(),
  depreciationAccountId: z.string().uuid("Invalid depreciation account ID").optional(),
  expenseAccountId: z.string().uuid("Invalid expense account ID").optional(),
  
  description: z.string().optional(),
  notes: z.string().optional(),
  
  // Reference fields
  reference: z.enum([
    AssetReference.VEHICLE,
    AssetReference.ACCOMMODATION,
    AssetReference.EVENT_SPACE,
    AssetReference.RENTAL_ITEM,
  ]).optional(),
  referenceId: z.string().uuid("Invalid reference ID").optional(),
  
  status: z.enum([
    AssetStatus.AVAILABLE,
    AssetStatus.UNAVAILABLE,
    AssetStatus.ASSIGNED,
    AssetStatus.DISCARDED,
    AssetStatus.LOST,
    AssetStatus.STOLEN,
    AssetStatus.DAMAGED,
    AssetStatus.MAINTENANCE,
    AssetStatus.ALLOCATED,
  ]).optional().default(AssetStatus.AVAILABLE),
});

// Backend DTO: UpdateAssetDto - Updated to match new schema
export const updateAssetSchema = z.object({
  assetCode: z.string().min(1, "Asset code is required").optional(),
  name: z.string().min(1, "Name is required").optional(),
  type: z.enum([
    AssetType.IT_EQUIPMENT,
    AssetType.FURNITURE,
    AssetType.VEHICLE,
    AssetType.MACHINERY,
    AssetType.BUILDING,
    AssetType.OFFICE_EQUIPMENT,
    AssetType.SOFTWARE,
    AssetType.OTHER,
  ]).optional(),
  
  // Supplier Details
  supplierId: z.string().uuid("Invalid supplier ID").optional(),
  
  purchaseDate: z.string().optional(),
  purchasePrice: z.string().optional(),
  purchaseOrderNumber: z.string().optional(),
  
  // Maintenance Details
  maintenanceFrequencyValue: z.number().min(1).optional(),
  maintenanceFrequency: z.enum([
    MaintenanceFrequency.DAYS,
    MaintenanceFrequency.WEEKS,
    MaintenanceFrequency.MONTHS,
    MaintenanceFrequency.YEARS,
  ]).optional(),
  lastMaintenanceDate: z.string().optional(),
  maintenanceDueDate: z.string().optional(),
  warrantyExpiryDate: z.string().optional(),
  warrantyPeriod: z.string().optional(),
  lifecycleExpiryDate: z.string().optional(),
  
  // Dynamic fields based on asset type
  key1: z.string().optional(),
  key2: z.string().optional(),
  key3: z.string().optional(),
  key4: z.string().optional(),
  key5: z.string().optional(),
  key6: z.string().optional(),
  key7: z.string().optional(),
  key8: z.string().optional(),
  key9: z.string().optional(),
  key10: z.string().optional(),
  
  categoryId: z.string().uuid("Invalid category ID").optional(),
  subCategoryId: z.string().uuid("Invalid sub-category ID").optional(),
  isAllocatedToAllLocations: z.boolean().optional(),
  bookValue: z.string().optional(),
  
  // Accounting Accounts
  fixedAssetAccountId: z.string().uuid("Invalid fixed asset account ID").optional(),
  depreciationAccountId: z.string().uuid("Invalid depreciation account ID").optional(),
  expenseAccountId: z.string().uuid("Invalid expense account ID").optional(),
  
  description: z.string().optional(),
  notes: z.string().optional(),
  
  // Reference fields
  reference: z.enum([
    AssetReference.VEHICLE,
    AssetReference.ACCOMMODATION,
    AssetReference.EVENT_SPACE,
    AssetReference.RENTAL_ITEM,
  ]).optional(),
  referenceId: z.string().uuid("Invalid reference ID").optional(),
  
  status: z.enum([
    AssetStatus.AVAILABLE,
    AssetStatus.UNAVAILABLE,
    AssetStatus.ASSIGNED,
    AssetStatus.DISCARDED,
    AssetStatus.LOST,
    AssetStatus.STOLEN,
    AssetStatus.DAMAGED,
    AssetStatus.MAINTENANCE,
    AssetStatus.ALLOCATED,
  ]).optional(),
});

// Schema for getting assets with pagination and filters
export const getAssetsSchema = z.object({
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).default(10),
  name: z.string().optional(),
  assetCode: z.string().optional(),
  status: z.string().optional(),
  type: z.string().optional(), // Changed from typeId to type
  categoryId: z.string().optional(), // Added categoryId
  subCategoryId: z.string().optional(), // Added subCategoryId
  locationId: z.string().optional(),
  from: z.string().optional(),
  to: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.union([z.string(), z.array(z.string())]),
        operator: z
          .enum([
            "iLike",
            "notILike",
            "eq",
            "ne",
            "isEmpty",
            "isNotEmpty",
            "lt",
            "lte",
            "gt",
            "gte",
            "isBetween",
            "isRelativeToToday",
          ])
          .default("iLike"),
        type: z
          .enum(["text", "number", "date", "boolean", "select", "multi-select"])
          .default("text"),
        rowId: z.string().optional(),
      })
    )
    .optional()
    .default([]),
  joinOperator: z.enum(["and", "or"]).default("and"),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional()
    .default([{ id: "createdAt", desc: true }]),
});

// Schema for updating asset status
export const updateAssetStatusSchema = z.object({
  id: z.string().min(1, "ID is required"),
  status: z.enum([
    AssetStatus.AVAILABLE,
    AssetStatus.UNAVAILABLE,
    AssetStatus.ASSIGNED,
    AssetStatus.DISCARDED,
    AssetStatus.LOST,
    AssetStatus.STOLEN,
    AssetStatus.DAMAGED,
    AssetStatus.MAINTENANCE,
    AssetStatus.ALLOCATED,
  ]),
});

// Schema for importing assets (includes status)
export const importAssetSchema = z.object({
  assetCode: z.string().min(1, "Asset code is required"),
  name: z.string().min(1, "Name is required"),
  serialNo: z.string().optional(),
  manufacturer: z.string().optional(),
  vendorId: z.string().uuid("Invalid vendor ID").optional(),
  vendorName: z.string().optional(),
  model: z.string().optional(),
  purchaseDate: z.string().optional(),
  purchasePrice: z.string().optional(),
  purchaseType: z.string().optional(),
  warrantyStartDate: z.string().optional(),
  warrantyEndDate: z.string().optional(),
  warrantyDetails: z.string().optional(),
  depreciationMethod: z.string().optional(),
  depreciationRate: z.string().optional(),
  depreciationStartDate: z.string().optional(),
  depreciationEndDate: z.string().optional(),
  depreciationDuration: z.string().optional(),
  depreciationDurationUnit: z.string().optional(),
  depreciationComputation: z.string().optional(),
  salvageValue: z.string().optional(),
  currentValue: z.string().optional(),
  lastDepreciationDate: z.string().optional(),
  nextDepreciationDate: z.string().optional(),
  totalDepreciation: z.string().optional(),
  accumulatedDepreciation: z.string().optional(),
  netBookValue: z.string().optional(),
  macAddress: z.string().optional(),
  ipAddress: z.string().optional(),
  hostname: z.string().optional(),
  domain: z.string().optional(),
  os: z.string().optional(),
  osVersion: z.string().optional(),
  totalPhysicalMemory: z.string().optional(),
  hardwareRemarks: z.string().optional(),
  typeId: z.string().uuid("Invalid type ID").optional(),
  subTypeId: z.string().uuid("Invalid sub-type ID").optional(),
  locationId: z.string().uuid("Invalid location ID").optional(),
  bookValue: z.string().optional(),
  fixedAssetAccountId: z
    .string()
    .uuid("Invalid fixed asset account ID")
    .optional(),
  depreciationAccountId: z
    .string()
    .uuid("Invalid depreciation account ID")
    .optional(),
  expenseAccountId: z.string().uuid("Invalid expense account ID").optional(),
  description: z.string().optional(),
  status: z
    .enum([
      AssetStatus.AVAILABLE,
      AssetStatus.UNAVAILABLE,
      AssetStatus.ASSIGNED,
      AssetStatus.DISCARDED,
      AssetStatus.LOST,
      AssetStatus.STOLEN,
      AssetStatus.DAMAGED,
      AssetStatus.MAINTENANCE,
      AssetStatus.ALLOCATED,
    ])
    .optional()
    .default(AssetStatus.AVAILABLE),
  imageIndex: z.number().min(0, "Image index must be 0 or greater").optional(),
});

// Bulk import schema for assets
export const bulkCreateAssetsSchema = z.array(createAssetSchema);

// Bulk import schema for assets with status
export const bulkImportAssetsSchema = z.array(importAssetSchema);

// Schema for bulk creation with images
export const bulkCreateAssetsWithImagesSchema = z.object({
  assets: z.array(createAssetSchema),
  images: z.array(z.instanceof(File)).optional(),
});

// Schema for bulk import with images
export const bulkImportAssetsWithImagesSchema = z.object({
  assets: z.array(importAssetSchema),
  images: z.array(z.instanceof(File)).optional(),
});

// Form schema for UI components (includes additional fields for form handling)
export const assetFormSchema = z.object({
  assetCode: z.string().min(1, "Asset code is required"),
  name: z.string().min(1, "Name is required"),
  serialNo: z.string().optional(),
  manufacturer: z.string().optional(),
  vendorId: z.string().uuid("Invalid vendor ID").optional(),
  vendorName: z.string().optional(),
  phone: z.string().optional(), // Contact phone number
  maintanceRemarks: z.string().optional(), // Maintenance remarks
  model: z.string().optional(),

  // Purchase Details
  serialNumber: z.string().optional(), // Different from serialNo
  purchaseDate: z.string().optional(),
  purchasePrice: z.string().optional(),
  purchaseOrderNumber: z.string().optional(),
  purchaseType: z.string().optional(),

  // Maintenance Details
  maintenanceFrequency: z.string().optional(),
  lastMaintainanceDate: z.string().optional(), // Next scheduled maintenance date
  warrantyExpiryDate: z.string().optional(),
  warrantyRemarks: z.string().optional(),
  warrantyPeriod: z.string().optional(),
  lifecycleExpiryDate: z.string().optional(),
  importantLinks: z.string().optional(),

  // Legacy warranty fields (keeping for backward compatibility)
  warrantyStartDate: z.string().optional(),
  warrantyEndDate: z.string().optional(),
  warrantyDetails: z.string().optional(),

  // Depreciation fields (keeping existing ones)
  depreciationMethod: z.string().optional(),
  depreciationRate: z.string().optional(),
  depreciationStartDate: z.string().optional(),
  depreciationEndDate: z.string().optional(),
  depreciationDuration: z.string().optional(),
  depreciationDurationUnit: z.string().optional(),
  depreciationComputation: z.string().optional(),
  salvageValue: z.string().optional(),
  currentValue: z.string().optional(),
  lastDepreciationDate: z.string().optional(),
  nextDepreciationDate: z.string().optional(),
  totalDepreciation: z.string().optional(),
  accumulatedDepreciation: z.string().optional(),
  netBookValue: z.string().optional(),

  // Hardware Details (for IT/Technology Assets)
  totalStorageSpace: z.string().optional(),
  imei: z.string().optional(),
  wifiMacAddress: z.string().optional(),
  ethernetMac: z.string().optional(),
  os: z.string().optional(),
  osVersion: z.string().optional(),
  totalPhysicalMemory: z.string().optional(),
  hardwareRemarks: z.string().optional(),

  // Legacy network fields (keeping for backward compatibility)
  macAddress: z.string().optional(),
  ipAddress: z.string().optional(),
  hostname: z.string().optional(),
  domain: z.string().optional(),

  // Asset categorization
  typeId: z.string().uuid("Invalid type ID").optional(),
  subTypeId: z.string().uuid("Invalid sub-type ID").optional(),
  locationId: z.string().uuid("Invalid location ID").optional(),

  // Financial/Accounting
  bookValue: z.string().optional(),
  fixedAssetAccountId: z
    .string()
    .uuid("Invalid fixed asset account ID")
    .optional(),
  depreciationAccountId: z
    .string()
    .uuid("Invalid depreciation account ID")
    .optional(),
  expenseAccountId: z.string().uuid("Invalid expense account ID").optional(),

  description: z.string().optional(),
  status: z
    .enum([
      AssetStatus.AVAILABLE,
      AssetStatus.UNAVAILABLE,
      AssetStatus.ASSIGNED,
      AssetStatus.DISCARDED,
      AssetStatus.LOST,
      AssetStatus.STOLEN,
      AssetStatus.DAMAGED,
      AssetStatus.MAINTENANCE,
      AssetStatus.ALLOCATED,
    ])
    .optional()
    .default(AssetStatus.AVAILABLE),
  imageIndex: z.number().min(0, "Image index must be 0 or greater").optional(),
});

// Schema for asset name availability check - Updated to include excludeId
export const checkAssetNameSchema = z.object({
  name: z.string().min(1, "Name is required"),
  excludeId: z.string().uuid("Invalid asset ID").optional(),
});

export type GetAssetsSchema = z.infer<typeof getAssetsSchema>;
export type CreateAssetSchema = z.infer<typeof createAssetSchema>;
export type ImportAssetSchema = z.infer<typeof importAssetSchema>;
export type UpdateAssetSchema = z.infer<typeof updateAssetSchema>;
export type UpdateAssetStatusSchema = z.infer<typeof updateAssetStatusSchema>;
export type BulkCreateAssetsSchema = z.infer<typeof bulkCreateAssetsSchema>;
export type BulkImportAssetsSchema = z.infer<typeof bulkImportAssetsSchema>;
export type BulkCreateAssetsWithImagesSchema = z.infer<
  typeof bulkCreateAssetsWithImagesSchema
>;
export type BulkImportAssetsWithImagesSchema = z.infer<
  typeof bulkImportAssetsWithImagesSchema
>;
export type AssetFormSchema = z.infer<typeof assetFormSchema>;
export type CheckAssetNameSchema = z.infer<typeof checkAssetNameSchema>;
