import {
  AssetDto,
  AssetSlimDto,
  AssetTableData,
  AssetStatus,
  AssetType,
  AssetIdResponse,
  BulkAssetIdsResponse,
  BulkDeleteAssetResponse,
  AssetNameAvailabilityResponse,
  SimpleAssetResponse,
  AssetResponse,
  CreateAssetDto,
  UpdateAssetDto,
  BulkCreateAssetDto,
  PaginatedAssetsOptimizedResponseDto,
  AssetListDto,
  BulkUpdateAssetStatusDto,
  BulkUpdateAssetStatusResponse,
} from "@/types/asset";
import { ApiStatus, ApiResponse } from "@/types/common";
import { GetAssetsSchema } from "./validations";

// Demo asset data - Updated to match new schema
export const demoAssets: AssetDto[] = [
  {
    id: "550e8400-e29b-41d4-a716-446655440000",
    businessId: "business-1",
    assetCode: "AST001",
    name: "Dell Laptop XPS 13",
    type: AssetType.IT_EQUIPMENT,
    status: AssetStatus.AVAILABLE,
    key1: "Dell", // Manufacturer in key1
    key2: "XPS 13", // Model in key2
    key3: "DL123456789", // Serial number in key3
    purchasePrice: "1299.99",
    purchaseDate: "2023-01-01",
    categoryId: "cat-it-equipment",
    isAllocatedToAllLocations: false,
    createdBy: "John Doe",
    createdAt: new Date("2023-01-01T00:00:00Z"),
    updatedAt: new Date("2023-01-01T00:00:00Z"),
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440001",
    businessId: "business-1",
    assetCode: "AST002",
    name: "HP Desktop Pro",
    type: AssetType.IT_EQUIPMENT,
    status: AssetStatus.ASSIGNED,
    key1: "HP", // Manufacturer in key1
    key2: "Pro 3000", // Model in key2
    key3: "HP987654321", // Serial number in key3
    purchasePrice: "899.99",
    purchaseDate: "2023-01-02",
    categoryId: "cat-it-equipment",
    isAllocatedToAllLocations: true,
    createdBy: "Jane Smith",
    createdAt: new Date("2023-01-02T00:00:00Z"),
    updatedAt: new Date("2023-01-02T00:00:00Z"),
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440002",
    businessId: "business-1",
    assetCode: "AST003",
    name: 'Samsung Monitor 27"',
    type: AssetType.IT_EQUIPMENT,
    status: AssetStatus.MAINTENANCE,
    key1: "Samsung", // Manufacturer in key1
    key2: '27" LED', // Model in key2
    key3: "SM555666777", // Serial number in key3
    purchasePrice: "299.99",
    purchaseDate: "2023-01-03",
    categoryId: "cat-it-equipment",
    isAllocatedToAllLocations: false,
    createdBy: "Bob Johnson",
    createdAt: new Date("2023-01-03T00:00:00Z"),
    updatedAt: new Date("2023-01-03T00:00:00Z"),
  },
];

// Demo slim assets
export const demoSlimAssets: AssetSlimDto[] = demoAssets.map((asset) => ({
  id: asset.id,
  name: asset.name,
  assetCode: asset.assetCode,
}));

// Demo list data - Updated to match AssetListDto
export const demoAssetListData: AssetListDto[] = demoAssets.map((asset) => ({
  id: asset.id,
  assetCode: asset.assetCode,
  name: asset.name,
  type: asset.type,
  status: asset.status,
  categoryId: asset.categoryId,
  categoryName: "IT Equipment", // Static value for demo
  purchaseDate: asset.purchaseDate,
  purchasePrice: asset.purchasePrice,
  bookValue: asset.bookValue,
  isAllocatedToAllLocations: asset.isAllocatedToAllLocations ?? false,
  locationsCount: asset.isAllocatedToAllLocations ? 3 : 1, // Calculated based on allocation
  description: asset.description,
  createdAt: asset.createdAt,
  updatedAt: asset.updatedAt,
}));

// Legacy demo table data for backward compatibility
export const demoAssetTableData: AssetTableData[] = demoAssets.map((asset) => ({
  id: asset.id,
  assetCode: asset.assetCode,
  name: asset.name,
  type: asset.type,
  status: asset.status,
  categoryId: asset.categoryId,
  subCategoryId: asset.subCategoryId,
  supplierId: asset.supplierId,
  purchaseDate: asset.purchaseDate,
  purchasePrice: asset.purchasePrice,
  createdAt: asset.createdAt,
  updatedAt: asset.updatedAt,
}));

// Demo functions for API simulation
export function getDemoAssetsApi(
  params: GetAssetsSchema
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const { page = 1, perPage = 10, name, assetCode, status } = params;

      let filteredAssets = [...demoAssetListData];

      // Apply filters
      if (name) {
        filteredAssets = filteredAssets.filter((asset) =>
          asset.name.toLowerCase().includes(name.toLowerCase())
        );
      }

      if (assetCode) {
        filteredAssets = filteredAssets.filter((asset) =>
          asset.assetCode.toLowerCase().includes(assetCode.toLowerCase())
        );
      }

      if (status) {
        filteredAssets = filteredAssets.filter(
          (asset) => asset.status === status
        );
      }

      // Pagination
      const startIndex = (page - 1) * perPage;
      const endIndex = startIndex + perPage;
      const paginatedData = filteredAssets.slice(startIndex, endIndex);

      const totalPages = Math.ceil(filteredAssets.length / perPage);

      const response: PaginatedAssetsOptimizedResponseDto = {
        data: paginatedData,
        meta: {
          total: filteredAssets.length,
          page,
          totalPages,
        },
      };

      resolve({
        status: ApiStatus.SUCCESS,
        message: "Assets retrieved successfully",
        data: response,
      });
    }, 500);
  });
}

export function getDemoAssetApi(id: string): Promise<AssetResponse> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const asset = demoAssets.find((a) => a.id === id);

      if (asset) {
        resolve({
          status: ApiStatus.SUCCESS,
          message: "Asset retrieved successfully",
          data: asset,
        });
      } else {
        resolve({
          status: ApiStatus.FAIL,
          message: "Asset not found",
          data: null,
        });
      }
    }, 300);
  });
}

export function getDemoAssetsSlimApi(): Promise<SimpleAssetResponse> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: ApiStatus.SUCCESS,
        message: "Slim assets retrieved successfully",
        data: demoSlimAssets,
      });
    }, 300);
  });
}

export function createDemoAssetApi(
  data: CreateAssetDto,
  images?: File[],
  attachments?: File[]
): Promise<AssetIdResponse> {
  // Simulate using data, images, and attachments
  console.log(
    "Creating asset with data:",
    data,
    "images:",
    images?.length,
    "attachments:",
    attachments?.length
  );

  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = `550e8400-e29b-41d4-a716-${Date.now()}`;

      resolve({
        status: ApiStatus.SUCCESS,
        message: "Asset created successfully",
        data: { id: newId },
      });
    }, 1000);
  });
}

export function updateDemoAssetApi(
  id: string,
  data: UpdateAssetDto,
  images?: File[],
  attachments?: File[]
): Promise<AssetIdResponse> {
  // Simulate using data, images, and attachments
  console.log(
    "Updating asset",
    id,
    "with data:",
    data,
    "images:",
    images?.length,
    "attachments:",
    attachments?.length
  );

  return new Promise((resolve) => {
    setTimeout(() => {
      const assetExists = demoAssets.some((a) => a.id === id);

      if (assetExists) {
        resolve({
          status: ApiStatus.SUCCESS,
          message: "Asset updated successfully",
          data: { id },
        });
      } else {
        resolve({
          status: ApiStatus.FAIL,
          message: "Asset not found",
          data: null,
        });
      }
    }, 800);
  });
}

export function deleteDemoAssetApi(
  id: string
): Promise<ApiResponse<{ id: string; message: string }>> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const assetExists = demoAssets.some((a) => a.id === id);

      if (assetExists) {
        resolve({
          status: ApiStatus.SUCCESS,
          message: "Asset deleted successfully",
          data: { id, message: "Asset deleted successfully" },
        });
      } else {
        resolve({
          status: ApiStatus.FAIL,
          message: "Asset not found",
          data: null,
        });
      }
    }, 600);
  });
}

export function bulkCreateDemoAssetsApi(
  assets: BulkCreateAssetDto[],
  images?: File[]
): Promise<BulkAssetIdsResponse> {
  // Simulate using images
  console.log(
    "Bulk creating assets:",
    assets.length,
    "images:",
    images?.length
  );

  return new Promise((resolve) => {
    setTimeout(() => {
      const ids = assets.map(() => `550e8400-e29b-41d4-a716-${Date.now()}`);

      resolve({
        status: ApiStatus.SUCCESS,
        message: `${assets.length} assets created successfully`,
        data: { ids },
      });
    }, 1500);
  });
}

export function bulkDeleteDemoAssetsApi(
  assetIds: string[]
): Promise<BulkDeleteAssetResponse> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: ApiStatus.SUCCESS,
        message: `${assetIds.length} assets deleted successfully`,
        data: {
          deletedCount: assetIds.length,
          deletedIds: assetIds,
        },
      });
    }, 1000);
  });
}

export function bulkUpdateDemoAssetStatusApi(
  data: BulkUpdateAssetStatusDto
): Promise<BulkUpdateAssetStatusResponse> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: ApiStatus.SUCCESS,
        message: `${data.ids.length} assets status updated successfully`,
        data: {
          updated: data.ids.length,
          updatedIds: data.ids,
          message: `Successfully updated ${data.ids.length} assets to ${data.status}`,
        },
      });
    }, 800);
  });
}

export function checkDemoAssetNameAvailabilityApi(
  name: string,
  excludeId?: string
): Promise<AssetNameAvailabilityResponse> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const isAvailable = !demoAssets.some(
        (asset) =>
          asset.name.toLowerCase() === name.toLowerCase() &&
          asset.id !== excludeId
      );

      resolve({
        status: ApiStatus.SUCCESS,
        message: "Name availability checked",
        data: {
          available: isAvailable,
          message: isAvailable ? "Name is available" : "Name is already taken",
        },
      });
    }, 400);
  });
}
