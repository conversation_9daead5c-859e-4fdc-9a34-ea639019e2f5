import {
  Asset<PERSON><PERSON>,
  AssetSlimDto,
  AssetStatus,
  AssetTableData,
  AssetType,
} from "@/types/asset";

// Format asset status for display
export function formatAssetStatus(status: AssetStatus): string {
  switch (status) {
    case AssetStatus.AVAILABLE:
      return "Available";
    case AssetStatus.UNAVAILABLE:
      return "Unavailable";
    case AssetStatus.ASSIGNED:
      return "Assigned";
    case AssetStatus.DISCARDED:
      return "Discarded";
    case AssetStatus.LOST:
      return "Lost";
    case AssetStatus.STOLEN:
      return "Stolen";
    case AssetStatus.DAMAGED:
      return "Damaged";
    case AssetStatus.MAINTENANCE:
      return "Maintenance";
    case AssetStatus.ALLOCATED:
      return "Allocated";
    default:
      return "Unknown";
  }
}

// Get status color for badges/indicators
export function getAssetStatusColor(status: AssetStatus): string {
  switch (status) {
    case AssetStatus.AVAILABLE:
      return "bg-green-100 text-green-800";
    case AssetStatus.UNAVAILABLE:
      return "bg-gray-100 text-gray-800";
    case AssetStatus.ASSIGNED:
      return "bg-blue-100 text-blue-800";
    case AssetStatus.DISCARDED:
      return "bg-red-100 text-red-800";
    case AssetStatus.LOST:
      return "bg-red-100 text-red-800";
    case AssetStatus.STOLEN:
      return "bg-red-100 text-red-800";
    case AssetStatus.DAMAGED:
      return "bg-orange-100 text-orange-800";
    case AssetStatus.MAINTENANCE:
      return "bg-yellow-100 text-yellow-800";
    case AssetStatus.ALLOCATED:
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Get status icon for display
export function getAssetStatusIcon(status: AssetStatus): string {
  switch (status) {
    case AssetStatus.AVAILABLE:
      return "✓";
    case AssetStatus.UNAVAILABLE:
      return "✗";
    case AssetStatus.ASSIGNED:
      return "👤";
    case AssetStatus.DISCARDED:
      return "🗑️";
    case AssetStatus.LOST:
      return "❓";
    case AssetStatus.STOLEN:
      return "⚠️";
    case AssetStatus.DAMAGED:
      return "🔧";
    case AssetStatus.MAINTENANCE:
      return "🛠️";
    case AssetStatus.ALLOCATED:
      return "📍";
    default:
      return "❓";
  }
}

// Check if asset is operational (available for use)
export function isAssetOperational(status: AssetStatus): boolean {
  return [AssetStatus.AVAILABLE, AssetStatus.ASSIGNED, AssetStatus.ALLOCATED].includes(status);
}

// Check if asset needs attention
export function assetNeedsAttention(status: AssetStatus): boolean {
  return [
    AssetStatus.DAMAGED,
    AssetStatus.MAINTENANCE,
    AssetStatus.LOST,
    AssetStatus.STOLEN,
  ].includes(status);
}

// Format asset code for display (uppercase)
export function formatAssetCode(assetCode: string): string {
  return assetCode.toUpperCase();
}

// Generate asset display name (combines name and asset code)
export function getAssetDisplayName(asset: AssetDto | AssetSlimDto | AssetTableData): string {
  const code = 'assetCode' in asset ? asset.assetCode : '';
  return `${asset.name} (${formatAssetCode(code)})`;
}

// Format currency values for display
export function formatCurrency(value: string | undefined, currency: string = "USD"): string {
  if (!value || value === "0" || value === "") return "-";
  
  const numValue = parseFloat(value);
  if (isNaN(numValue)) return "-";
  
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
  }).format(numValue);
}

// Format date for display
export function formatAssetDate(date: string | Date | undefined): string {
  if (!date) return "-";
  
  const dateObj = typeof date === "string" ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return "-";
  
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(dateObj);
}

// Calculate asset age in years
export function calculateAssetAge(purchaseDate: string | Date | undefined): number {
  if (!purchaseDate) return 0;
  
  const purchaseDateObj = typeof purchaseDate === "string" ? new Date(purchaseDate) : purchaseDate;
  if (isNaN(purchaseDateObj.getTime())) return 0;
  
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - purchaseDateObj.getTime());
  const diffYears = diffTime / (1000 * 60 * 60 * 24 * 365.25);
  
  return Math.floor(diffYears * 10) / 10; // Round to 1 decimal place
}

// Format asset age for display
export function formatAssetAge(purchaseDate: string | Date | undefined): string {
  const age = calculateAssetAge(purchaseDate);
  if (age === 0) return "-";
  
  if (age < 1) {
    const months = Math.floor(age * 12);
    return months === 1 ? "1 month" : `${months} months`;
  }
  
  return age === 1 ? "1 year" : `${age} years`;
}

// Check if warranty is still valid
export function isWarrantyValid(warrantyEndDate: string | Date | undefined): boolean {
  if (!warrantyEndDate) return false;
  
  const endDate = typeof warrantyEndDate === "string" ? new Date(warrantyEndDate) : warrantyEndDate;
  if (isNaN(endDate.getTime())) return false;
  
  return endDate > new Date();
}

// Get warranty status for display
export function getWarrantyStatus(warrantyEndDate: string | Date | undefined): {
  status: "valid" | "expired" | "unknown";
  label: string;
  color: string;
} {
  if (!warrantyEndDate) {
    return {
      status: "unknown",
      label: "Unknown",
      color: "bg-gray-100 text-gray-800",
    };
  }
  
  const isValid = isWarrantyValid(warrantyEndDate);
  
  if (isValid) {
    return {
      status: "valid",
      label: "Valid",
      color: "bg-green-100 text-green-800",
    };
  } else {
    return {
      status: "expired",
      label: "Expired",
      color: "bg-red-100 text-red-800",
    };
  }
}

// Validate asset code format (alphanumeric with optional hyphens/underscores)
export function isValidAssetCode(assetCode: string): boolean {
  const assetCodeRegex = /^[A-Za-z0-9_-]+$/;
  return assetCodeRegex.test(assetCode) && assetCode.length >= 2 && assetCode.length <= 20;
}

// Generate suggested asset code based on name
export function generateAssetCode(name: string, existingCodes: string[] = []): string {
  // Take first 3 characters of each word, remove spaces and special chars
  const words = name.split(/\s+/).filter(word => word.length > 0);
  let code = words
    .map(word => word.substring(0, 3).toUpperCase())
    .join("")
    .replace(/[^A-Z0-9]/g, "");
  
  // Ensure minimum length
  if (code.length < 3) {
    code = code.padEnd(3, "0");
  }
  
  // Ensure uniqueness
  let counter = 1;
  let uniqueCode = code;
  while (existingCodes.includes(uniqueCode)) {
    uniqueCode = `${code}${counter.toString().padStart(2, "0")}`;
    counter++;
  }
  
  return uniqueCode;
}

// Sort assets by various criteria
export function sortAssets<T extends AssetTableData>(
  assets: T[],
  sortBy: "name" | "assetCode" | "status" | "createdAt" | "type",
  direction: "asc" | "desc" = "asc"
): T[] {
  return [...assets].sort((a, b) => {
    let aValue: string | number | Date;
    let bValue: string | number | Date;
    
    switch (sortBy) {
      case "name":
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case "assetCode":
        aValue = a.assetCode.toLowerCase();
        bValue = b.assetCode.toLowerCase();
        break;
      case "status":
        aValue = a.status;
        bValue = b.status;
        break;
      case "type":
        aValue = a.type;
        bValue = b.type;
        break;
      case "createdAt":
        aValue = new Date(a.createdAt);
        bValue = new Date(b.createdAt);
        break;
      default:
        return 0;
    }
    
    if (aValue < bValue) return direction === "asc" ? -1 : 1;
    if (aValue > bValue) return direction === "asc" ? 1 : -1;
    return 0;
  });
}

// Filter assets by status
export function filterAssetsByStatus<T extends AssetTableData>(
  assets: T[],
  statuses: AssetStatus[]
): T[] {
  return assets.filter(asset => statuses.includes(asset.status));
}

// Search assets by text query
export function searchAssets<T extends AssetTableData>(
  assets: T[],
  query: string
): T[] {
  if (!query.trim()) return assets;
  
  const searchTerm = query.toLowerCase().trim();
  
  return assets.filter(asset => 
    asset.name.toLowerCase().includes(searchTerm) ||
    asset.assetCode.toLowerCase().includes(searchTerm) ||
    asset.type.toLowerCase().includes(searchTerm)
  );
}

// Get asset statistics
export function getAssetStatistics(assets: AssetTableData[]): {
  total: number;
  available: number;
  assigned: number;
  maintenance: number;
  damaged: number;
  other: number;
} {
  const stats = {
    total: assets.length,
    available: 0,
    assigned: 0,
    maintenance: 0,
    damaged: 0,
    other: 0,
  };
  
  assets.forEach(asset => {
    switch (asset.status) {
      case AssetStatus.AVAILABLE:
        stats.available++;
        break;
      case AssetStatus.ASSIGNED:
      case AssetStatus.ALLOCATED:
        stats.assigned++;
        break;
      case AssetStatus.MAINTENANCE:
        stats.maintenance++;
        break;
      case AssetStatus.DAMAGED:
        stats.damaged++;
        break;
      default:
        stats.other++;
        break;
    }
  });
  
  return stats;
}

// Define what each key represents for different asset types
export const ASSET_TYPE_KEY_MAPPINGS = {
  [AssetType.IT_EQUIPMENT]: {
    key1: 'Serial Number',
    key2: 'Model Number',
    key3: 'Total Storage Space',
    key4: 'IMEI',
    key5: 'WiFi MAC Address',
    key6: 'Ethernet MAC',
    key7: 'Operating System',
    key8: 'OS Version',
    key9: 'Total Physical Memory',
    key10: 'Processor',
  },
  [AssetType.VEHICLE]: {
    key1: 'VIN',
    key2: 'Model Number',
    key3: 'Vehicle Registration Number',
    key4: 'Engine Number',
    key5: 'Fuel Type',
    key6: 'Color',
    key7: 'Year of Manufacture',
    key8: 'Mileage',
    key9: 'Insurance Policy Number',
    key10: 'Insurance Expiry Date',
  },
  [AssetType.FURNITURE]: {
    key1: 'Serial Number',
    key2: 'Model Number',
    key3: 'Material',
    key4: 'Color',
    key5: 'Dimensions',
    key6: 'Weight',
    key7: 'Assembly Required',
    key8: 'Room/Location',
    key9: 'Furniture Type',
    key10: 'Condition',
  },
  [AssetType.MACHINERY]: {
    key1: 'Serial Number',
    key2: 'Model Number',
    key3: 'Power Rating',
    key4: 'Voltage',
    key5: 'Weight',
    key6: 'Dimensions',
    key7: 'Operating Temperature',
    key8: 'Safety Certification',
    key9: 'Capacity',
    key10: 'Service Contract Number',
  },
  [AssetType.BUILDING]: {
    key1: 'Property ID',
    key2: 'Building Code',
    key3: 'Built-up Area',
    key4: 'Land Area',
    key5: 'Number of Floors',
    key6: 'Construction Year',
    key7: 'Property Tax Number',
    key8: 'Electricity Connection Number',
    key9: 'Water Connection Number',
    key10: 'Occupancy Certificate Number',
  },
  [AssetType.OFFICE_EQUIPMENT]: {
    key1: 'Serial Number',
    key2: 'Model Number',
    key3: 'Equipment Type',
    key4: 'Power Consumption',
    key5: 'Connectivity Type',
    key6: 'Capacity',
    key7: 'Speed',
    key8: 'Color/Finish',
    key9: 'Dimensions',
    key10: 'Special Features',
  },
  [AssetType.SOFTWARE]: {
    key1: 'License Key',
    key2: 'Product Version',
    key3: 'License Type',
    key4: 'Number of Users',
    key5: 'Platform',
    key6: 'Subscription Start Date',
    key7: 'Subscription End Date',
    key8: 'Vendor Support ID',
    key9: 'Installation Count',
    key10: 'Activation Code',
  },
  [AssetType.OTHER]: {
    key1: 'Serial Number',
    key2: 'Model Number',
    key3: 'Custom Field 3',
    key4: 'Custom Field 4',
    key5: 'Custom Field 5',
    key6: 'Custom Field 6',
    key7: 'Custom Field 7',
    key8: 'Custom Field 8',
    key9: 'Custom Field 9',
    key10: 'Custom Field 10',
  },
};

// Get field label for a specific asset type and key
export function getAssetFieldLabel(assetType: AssetType, keyName: keyof typeof ASSET_TYPE_KEY_MAPPINGS[AssetType.IT_EQUIPMENT]): string {
  return ASSET_TYPE_KEY_MAPPINGS[assetType]?.[keyName] || keyName;
}

// Get all field mappings for a specific asset type
export function getAssetTypeFields(assetType: AssetType): Record<string, string> {
  return ASSET_TYPE_KEY_MAPPINGS[assetType] || ASSET_TYPE_KEY_MAPPINGS[AssetType.OTHER];
}

// Check if a field is relevant for a specific asset type
export function isFieldRelevantForAssetType(assetType: AssetType, keyName: string): boolean {
  const mapping = ASSET_TYPE_KEY_MAPPINGS[assetType];
  return mapping ? keyName in mapping : false;
}
