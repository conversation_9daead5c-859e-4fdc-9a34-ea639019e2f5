import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";
import {
  AssetIdResponse,
  BulkAssetIdsResponse,
  BulkDeleteAssetResponse,
  AssetNameAvailabilityResponse,
  SimpleAssetResponse,
  AssetResponse,
  CreateAssetDto,
  UpdateAssetDto,
  BulkCreateAssetDto,
  AssetStatus,
  BulkUpdateAssetStatusDto,
  BulkUpdateAssetStatusResponse,
  PaginatedAssetsOptimizedResponseDto,
} from "@/types/asset";
import { ApiResponse } from "@/types/common";
import {
  GetAssetsSchema,
  BulkImportAssetsWithImagesSchema,
} from "./validations";
import {
  getAssetsTableData,
  getAssetsSlim,
  getAsset,
  checkAssetNameAvailability,
  createAsset,
  bulkCreateAssets,
  updateAsset,
  deleteAsset,
  bulkDeleteAssets,
  updateAssetStatus,
  bulkUpdateAssetStatus,
  bulkImportAssetsWithImages,
} from "./queries";

// Query keys for cache management
export const assetKeys = {
  all: ["assets"] as const,
  list: () => [...assetKeys.all, "list"] as const,
  filtered: (params: GetAssetsSchema & { isDemo?: boolean }) =>
    [...assetKeys.list(), params] as const,
  simple: () => [...assetKeys.all, "simple"] as const,
  detail: (id: string) => [...assetKeys.all, "detail", id] as const,
  nameAvailability: (name: string) =>
    [...assetKeys.all, "name-availability", name] as const,
};

// Hook to get assets with pagination and filters
export function useAssetsData(
  params: GetAssetsSchema,
  isDemo: boolean = false
): UseQueryResult<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  return useQuery({
    queryKey: assetKeys.filtered({ ...params, isDemo }),
    queryFn: () => getAssetsTableData(params, isDemo),
    staleTime: 1000 * 60 * 5, // Always fetch fresh data when params change
    refetchOnMount: true, // Ensure it fetches on mount
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    placeholderData: (previousData) => previousData, // Keep previous data while loading new data
  });
}

// Hook to get assets in slim format (for dropdowns)
export function useAssetsSlim(
  isDemo: boolean = false
): UseQueryResult<SimpleAssetResponse> {
  return useQuery({
    queryKey: [...assetKeys.simple(), { isDemo }],
    queryFn: () => getAssetsSlim(isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get a single asset by ID
export function useAsset(
  id: string,
  isDemo: boolean = false
): UseQueryResult<AssetResponse> {
  return useQuery({
    queryKey: [...assetKeys.detail(id), { isDemo }],
    queryFn: () => getAsset(id, isDemo),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to check asset name availability
export function useAssetNameAvailability(
  name: string,
  isDemo: boolean = false
): UseQueryResult<AssetNameAvailabilityResponse> {
  return useQuery({
    queryKey: [...assetKeys.nameAvailability(name), { isDemo }],
    queryFn: () => checkAssetNameAvailability(name, isDemo),
    enabled: !!name && name.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Hook to create a new asset
export function useCreateAsset(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ data, image }: { data: CreateAssetDto; image?: File }) =>
      createAsset(data, image, isDemo),
    onSuccess: () => {
      // Invalidate and refetch asset queries
      queryClient.invalidateQueries({ queryKey: assetKeys.all });
    },
  });
}

// Hook to bulk create assets
export function useBulkCreateAssets(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      assets,
      images,
    }: {
      assets: BulkCreateAssetDto[];
      images?: File[];
    }) => bulkCreateAssets(assets, images, isDemo),
    onSuccess: () => {
      // Invalidate and refetch asset queries
      queryClient.invalidateQueries({ queryKey: assetKeys.all });
    },
  });
}

// Hook to update an asset
export function useUpdateAsset(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
      image,
    }: {
      id: string;
      data: UpdateAssetDto;
      image?: File;
    }) => updateAsset(id, data, image, isDemo),
    onSuccess: (_, { id }) => {
      // Invalidate and refetch asset queries
      queryClient.invalidateQueries({ queryKey: assetKeys.all });
      queryClient.invalidateQueries({ queryKey: assetKeys.detail(id) });
    },
  });
}

// Hook to delete an asset
export function useDeleteAsset(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteAsset(id, isDemo),
    onSuccess: (_, id) => {
      // Invalidate and refetch asset queries
      queryClient.invalidateQueries({ queryKey: assetKeys.all });
      queryClient.removeQueries({ queryKey: assetKeys.detail(id) });
    },
  });
}

// Hook to bulk delete assets
export function useBulkDeleteAssets(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (assetIds: string[]) => bulkDeleteAssets(assetIds, isDemo),
    onSuccess: (_, assetIds) => {
      // Invalidate and refetch asset queries
      queryClient.invalidateQueries({ queryKey: assetKeys.all });
      // Remove individual asset queries
      assetIds.forEach((id) => {
        queryClient.removeQueries({ queryKey: assetKeys.detail(id) });
      });
    },
  });
}

// Hook to update asset status
export function useUpdateAssetStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: AssetStatus }) =>
      updateAssetStatus(id, status, isDemo),
    onSuccess: (_, { id }) => {
      // Invalidate and refetch asset queries
      queryClient.invalidateQueries({ queryKey: assetKeys.all });
      queryClient.invalidateQueries({ queryKey: assetKeys.detail(id) });
    },
  });
}

// Hook to bulk update asset status
export function useBulkUpdateAssetStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkUpdateAssetStatusDto) =>
      bulkUpdateAssetStatus(data, isDemo),
    onSuccess: (_, { ids }) => {
      // Invalidate and refetch asset queries
      queryClient.invalidateQueries({ queryKey: assetKeys.all });
      // Invalidate individual asset queries
      ids.forEach((id) => {
        queryClient.invalidateQueries({ queryKey: assetKeys.detail(id) });
      });
    },
  });
}

// Hook to bulk import assets with images
export function useBulkImportAssetsWithImages(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkImportAssetsWithImagesSchema) =>
      bulkImportAssetsWithImages(data, isDemo),
    onSuccess: () => {
      // Invalidate and refetch asset queries
      queryClient.invalidateQueries({ queryKey: assetKeys.all });
    },
  });
}

// Utility hooks for common operations

// Hook to get asset status options
export function useAssetStatusOptions() {
  return Object.values(AssetStatus).map((status) => ({
    value: status,
    label: status.charAt(0).toUpperCase() + status.slice(1).toLowerCase(),
  }));
}

// Hook to get filtered assets by status
export function useAssetsByStatus(
  status: AssetStatus,
  isDemo: boolean = false
): UseQueryResult<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  const params: GetAssetsSchema = {
    page: 1,
    perPage: 100,
    status,
    filters: [],
    joinOperator: "and",
    sort: [{ id: "createdAt", desc: true }],
  };

  return useAssetsData(params, isDemo);
}

// Hook to get available assets
export function useAvailableAssets(
  isDemo: boolean = false
): UseQueryResult<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  return useAssetsByStatus(AssetStatus.AVAILABLE, isDemo);
}

// Hook to get assigned assets
export function useAssignedAssets(
  isDemo: boolean = false
): UseQueryResult<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  return useAssetsByStatus(AssetStatus.ASSIGNED, isDemo);
}

// Hook to get assets in maintenance
export function useMaintenanceAssets(
  isDemo: boolean = false
): UseQueryResult<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  return useAssetsByStatus(AssetStatus.MAINTENANCE, isDemo);
}
