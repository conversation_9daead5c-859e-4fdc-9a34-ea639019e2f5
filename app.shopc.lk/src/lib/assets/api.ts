import {
  AssetResponse,
  AssetIdResponse,
  BulkAssetIdsResponse,
  BulkDeleteAssetDto,
  BulkDeleteAssetResponse,
  SimpleAssetResponse,
  CreateAssetDto,
  UpdateAssetDto,
  BulkCreateAssetDto,
  AssetStatus,
  BulkUpdateAssetStatusDto,
  BulkUpdateAssetStatusResponse,
  PaginatedAssetsOptimizedResponseDto,
  CreateAssetImageDto,
  UpdateAssetImagesSortOrderDto,
  AssetAttachmentDto,
  CheckAssetNameDto,
  CheckAssetNameResponseDto,
} from "@/types/asset";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetAssetsSchema } from "./validations";
import axios from "@/utils/axios";

// Create a new asset
export async function createAssetApi(
  data: CreateAssetDto,
  images?: File[],
  attachments?: File[]
): Promise<AssetIdResponse> {
  try {
    const formData = new FormData();
    formData.append("assetCode", data.assetCode);
    formData.append("name", data.name);
    if (data.type) formData.append("type", data.type);
    if (data.supplierId) formData.append("supplierId", data.supplierId);
    if (data.purchaseDate) formData.append("purchaseDate", data.purchaseDate);
    if (data.purchasePrice) formData.append("purchasePrice", data.purchasePrice);
    if (data.purchaseOrderNumber) formData.append("purchaseOrderNumber", data.purchaseOrderNumber);
    if (data.maintenanceFrequencyValue !== undefined) formData.append("maintenanceFrequencyValue", data.maintenanceFrequencyValue.toString());
    if (data.maintenanceFrequency) formData.append("maintenanceFrequency", data.maintenanceFrequency);
    if (data.lastMaintenanceDate) formData.append("lastMaintenanceDate", data.lastMaintenanceDate);
    if (data.maintenanceDueDate) formData.append("maintenanceDueDate", data.maintenanceDueDate);
    if (data.warrantyExpiryDate) formData.append("warrantyExpiryDate", data.warrantyExpiryDate);
    if (data.warrantyPeriod) formData.append("warrantyPeriod", data.warrantyPeriod);
    if (data.lifecycleExpiryDate) formData.append("lifecycleExpiryDate", data.lifecycleExpiryDate);
    
    // Dynamic fields
    if (data.key1) formData.append("key1", data.key1);
    if (data.key2) formData.append("key2", data.key2);
    if (data.key3) formData.append("key3", data.key3);
    if (data.key4) formData.append("key4", data.key4);
    if (data.key5) formData.append("key5", data.key5);
    if (data.key6) formData.append("key6", data.key6);
    if (data.key7) formData.append("key7", data.key7);
    if (data.key8) formData.append("key8", data.key8);
    if (data.key9) formData.append("key9", data.key9);
    if (data.key10) formData.append("key10", data.key10);
    
    if (data.categoryId) formData.append("categoryId", data.categoryId);
    if (data.subCategoryId) formData.append("subCategoryId", data.subCategoryId);
    if (data.isAllocatedToAllLocations !== undefined) formData.append("isAllocatedToAllLocations", data.isAllocatedToAllLocations.toString());
    if (data.bookValue) formData.append("bookValue", data.bookValue);
    if (data.fixedAssetAccountId) formData.append("fixedAssetAccountId", data.fixedAssetAccountId);
    if (data.depreciationAccountId) formData.append("depreciationAccountId", data.depreciationAccountId);
    if (data.expenseAccountId) formData.append("expenseAccountId", data.expenseAccountId);
    if (data.description) formData.append("description", data.description);
    if (data.notes) formData.append("notes", data.notes);
    if (data.reference) formData.append("reference", data.reference);
    if (data.referenceId) formData.append("referenceId", data.referenceId);
    if (data.status) formData.append("status", data.status);
    
    // Add images
    if (images) {
      images.forEach((image) => {
        formData.append("images", image);
      });
    }
    
    // Add attachments
    if (attachments) {
      attachments.forEach((attachment) => {
        formData.append("attachments", attachment);
      });
    }

    const res = await axios.post("/assets", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk create assets
export async function bulkCreateAssetsApi(
  assets: BulkCreateAssetDto[],
  images?: File[]
): Promise<BulkAssetIdsResponse> {
  try {
    const formData = new FormData();
    formData.append("assets", JSON.stringify(assets));

    if (images) {
      images.forEach((image) => {
        formData.append("images", image);
      });
    }

    const res = await axios.post("/assets/bulk", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get assets with pagination and filters (using optimized endpoint)
export async function getAssetsApi(
  params: GetAssetsSchema
): Promise<ApiResponse<PaginatedAssetsOptimizedResponseDto>> {
  try {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append("page", String(params.page));
    if (params.perPage) searchParams.append("limit", String(params.perPage));
    if (params.name) searchParams.append("name", params.name);
    if (params.assetCode) searchParams.append("assetCode", params.assetCode);
    if (params.status) searchParams.append("status", params.status);
    if (params.type) searchParams.append("type", params.type);
    if (params.categoryId) searchParams.append("categoryId", params.categoryId);
    if (params.subCategoryId) searchParams.append("subCategoryId", params.subCategoryId);
    if (params.locationId) searchParams.append("locationId", params.locationId);
    if (params.from) searchParams.append("from", params.from);
    if (params.to) searchParams.append("to", params.to);
    if (params.filters && params.filters.length > 0) {
      searchParams.append("filters", JSON.stringify(params.filters));
    }
    if (params.joinOperator)
      searchParams.append("joinOperator", params.joinOperator);
    if (params.sort && params.sort.length > 0) {
      // Backend expects sort as "field:direction" format
      const sortParam = params.sort
        .map(
          (s: { id: string; desc: boolean }) =>
            `${s.id}:${s.desc ? "desc" : "asc"}`
        )
        .join(",");
      searchParams.append("sort", sortParam);
    }

    const res = await axios.get(`/assets?${searchParams.toString()}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check asset name availability
export async function checkAssetNameAvailabilityApi(
  data: CheckAssetNameDto
): Promise<ApiResponse<CheckAssetNameResponseDto>> {
  try {
    const searchParams = new URLSearchParams();
    searchParams.append("name", data.name);
    if (data.excludeId) searchParams.append("excludeId", data.excludeId);
    
    const res = await axios.get(
      `/assets/check-name?${searchParams.toString()}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get assets in slim format (for dropdowns/selects)
export async function getAssetsSlimApi(): Promise<SimpleAssetResponse> {
  try {
    const res = await axios.get("/assets/slim");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get a single asset by ID
export async function getAssetApi(id: string): Promise<AssetResponse> {
  try {
    const res = await axios.get(`/assets/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update an asset
export async function updateAssetApi(
  id: string,
  data: UpdateAssetDto,
  images?: File[],
  attachments?: File[]
): Promise<AssetIdResponse> {
  try {
    const formData = new FormData();
    if (data.assetCode !== undefined) formData.append("assetCode", data.assetCode);
    if (data.name !== undefined) formData.append("name", data.name);
    if (data.type !== undefined) formData.append("type", data.type || "");
    if (data.supplierId !== undefined) formData.append("supplierId", data.supplierId || "");
    if (data.purchaseDate !== undefined) formData.append("purchaseDate", data.purchaseDate || "");
    if (data.purchasePrice !== undefined) formData.append("purchasePrice", data.purchasePrice || "");
    if (data.purchaseOrderNumber !== undefined) formData.append("purchaseOrderNumber", data.purchaseOrderNumber || "");
    if (data.maintenanceFrequencyValue !== undefined) formData.append("maintenanceFrequencyValue", data.maintenanceFrequencyValue?.toString() || "");
    if (data.maintenanceFrequency !== undefined) formData.append("maintenanceFrequency", data.maintenanceFrequency || "");
    if (data.lastMaintenanceDate !== undefined) formData.append("lastMaintenanceDate", data.lastMaintenanceDate || "");
    if (data.maintenanceDueDate !== undefined) formData.append("maintenanceDueDate", data.maintenanceDueDate || "");
    if (data.warrantyExpiryDate !== undefined) formData.append("warrantyExpiryDate", data.warrantyExpiryDate || "");
    if (data.warrantyPeriod !== undefined) formData.append("warrantyPeriod", data.warrantyPeriod || "");
    if (data.lifecycleExpiryDate !== undefined) formData.append("lifecycleExpiryDate", data.lifecycleExpiryDate || "");
    
    // Dynamic fields
    if (data.key1 !== undefined) formData.append("key1", data.key1 || "");
    if (data.key2 !== undefined) formData.append("key2", data.key2 || "");
    if (data.key3 !== undefined) formData.append("key3", data.key3 || "");
    if (data.key4 !== undefined) formData.append("key4", data.key4 || "");
    if (data.key5 !== undefined) formData.append("key5", data.key5 || "");
    if (data.key6 !== undefined) formData.append("key6", data.key6 || "");
    if (data.key7 !== undefined) formData.append("key7", data.key7 || "");
    if (data.key8 !== undefined) formData.append("key8", data.key8 || "");
    if (data.key9 !== undefined) formData.append("key9", data.key9 || "");
    if (data.key10 !== undefined) formData.append("key10", data.key10 || "");
    
    if (data.categoryId !== undefined) formData.append("categoryId", data.categoryId || "");
    if (data.subCategoryId !== undefined) formData.append("subCategoryId", data.subCategoryId || "");
    if (data.isAllocatedToAllLocations !== undefined) formData.append("isAllocatedToAllLocations", data.isAllocatedToAllLocations?.toString() || "false");
    if (data.bookValue !== undefined) formData.append("bookValue", data.bookValue || "");
    if (data.fixedAssetAccountId !== undefined) formData.append("fixedAssetAccountId", data.fixedAssetAccountId || "");
    if (data.depreciationAccountId !== undefined) formData.append("depreciationAccountId", data.depreciationAccountId || "");
    if (data.expenseAccountId !== undefined) formData.append("expenseAccountId", data.expenseAccountId || "");
    if (data.description !== undefined) formData.append("description", data.description || "");
    if (data.notes !== undefined) formData.append("notes", data.notes || "");
    if (data.reference !== undefined) formData.append("reference", data.reference || "");
    if (data.referenceId !== undefined) formData.append("referenceId", data.referenceId || "");
    if (data.status) formData.append("status", data.status);
    
    // Add images
    if (images) {
      images.forEach((image) => {
        formData.append("images", image);
      });
    }
    
    // Add attachments
    if (attachments) {
      attachments.forEach((attachment) => {
        formData.append("attachments", attachment);
      });
    }

    const res = await axios.patch(`/assets/${id}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Delete an asset
export async function deleteAssetApi(
  id: string
): Promise<ApiResponse<{ id: string; message: string }>> {
  try {
    const res = await axios.delete(`/assets/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk delete assets
export async function bulkDeleteAssetsApi(
  assetIds: string[]
): Promise<BulkDeleteAssetResponse> {
  try {
    const data: BulkDeleteAssetDto = { ids: assetIds };
    const res = await axios.delete("/assets/bulk", { data });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update asset status (convenience function)
export async function updateAssetStatusApi(
  id: string,
  status: AssetStatus
): Promise<AssetIdResponse> {
  try {
    const formData = new FormData();
    formData.append("status", status);

    const res = await axios.patch(`/assets/${id}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Asset image management functions
export async function addAssetImagesApi(
  assetId: string,
  images: CreateAssetImageDto[]
): Promise<ApiResponse<{ images: { id: string }[] }>> {
  try {
    const res = await axios.post(`/assets/${assetId}/images`, images);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

export async function updateAssetImagesSortOrderApi(
  assetId: string,
  updates: UpdateAssetImagesSortOrderDto
): Promise<ApiResponse<{ updated: number }>> {
  try {
    const res = await axios.patch(`/assets/${assetId}/images/sort-order`, updates);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

export async function removeAssetImageApi(
  assetId: string,
  imageId: string
): Promise<ApiResponse<{ deleted: boolean }>> {
  try {
    const res = await axios.delete(`/assets/${assetId}/images/${imageId}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

export async function setAssetPrimaryImageApi(
  assetId: string,
  imageId: string
): Promise<ApiResponse<{ updated: boolean }>> {
  try {
    const res = await axios.patch(`/assets/${assetId}/images/${imageId}/primary`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Asset attachment management functions
export async function getAssetAttachmentsApi(
  assetId: string
): Promise<ApiResponse<{ attachments: AssetAttachmentDto[] }>> {
  try {
    const res = await axios.get(`/assets/${assetId}/attachments`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

export async function addAssetAttachmentsApi(
  assetId: string,
  attachments: File[]
): Promise<ApiResponse<{ attachments: { id: string; fileName: string; originalName: string }[] }>> {
  try {
    const formData = new FormData();
    attachments.forEach((attachment) => {
      formData.append("attachments", attachment);
    });

    const res = await axios.post(`/assets/${assetId}/attachments`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

export async function removeAssetAttachmentApi(
  assetId: string,
  attachmentId: string
): Promise<ApiResponse<{ deleted: boolean }>> {
  try {
    const res = await axios.delete(`/assets/${assetId}/attachments/${attachmentId}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk update asset status
export async function bulkUpdateAssetStatusApi(
  data: BulkUpdateAssetStatusDto
): Promise<BulkUpdateAssetStatusResponse> {
  try {
    const res = await axios.patch("/assets/bulk-status", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}
