import { ApiResponse } from "./common";

export enum CategoryStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

// Backend DTO: CategoryDto
export interface CategoryDto {
  id: string;
  businessId: string;
  name: string;
  shortCode?: string;
  description?: string;
  slug?: string;
  parentId?: string;
  availableOnline: boolean;
  position: number;
  color?: string;
  status: CategoryStatus;
  productsCount: number;
  // Location fields
  isAllocatedToAllLocations: boolean;
  locations: { id: string; name: string }[]; // Empty array when allocated to all locations
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  ogImage?: string;
  image?: string;
  parent?: CategorySlimDto;
  subcategories?: CategorySlimDto[];
  createdBy: string;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: CreateCategoryDto
export interface CreateCategoryDto {
  name: string;
  shortCode?: string;
  parentId?: string;
  description?: string;
  slug?: string;
  availableOnline?: boolean;
  color?: string;
  status?: CategoryStatus;
  // Location fields
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[]; // For junction table management (ignored if isAllocatedToAllLocations is true)
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

// Backend DTO: UpdateCategoryDto
export interface UpdateCategoryDto {
  name?: string;
  shortCode?: string;
  parentId?: string;
  description?: string;
  slug?: string;
  availableOnline?: boolean;
  color?: string;
  status?: CategoryStatus;
  // Location fields
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[]; // For junction table management (ignored if isAllocatedToAllLocations is true)
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

// Backend DTO: CategorySlimDto
export interface CategorySlimDto {
  id: string;
  name: string;
  position: number;
  media?: {
    id: string;
    publicUrl: string;
    originalName: string;
  };
  subcategories?: CategorySlimDto[];
}

// Backend DTO: PaginatedCategoriesResponseDto
export interface PaginationMetaDto {
  total: number;
  page: number;
  totalPages: number;
}

export interface PaginatedCategoriesResponseDto {
  data: CategoryTableData[];
  meta: PaginationMetaDto;
}

// Backend DTO: DeleteCategoryResponseDto
export interface DeleteCategoryResponseDto {
  success: boolean;
  message: string;
}

// Backend DTO: CategoryNameAvailabilityResponseDto
export interface CategoryNameAvailabilityResponseDto {
  available: boolean;
}

// Backend DTO: CategoryIdResponseDto
export interface CategoryIdResponseDto {
  id: string;
}

// Backend DTO: BulkCategoryIdsResponseDto
export interface BulkCategoryIdsResponseDto {
  ids: string[];
}

// Backend DTO: BulkDeleteCategoryDto
export interface BulkDeleteCategoryDto {
  categoryIds: string[];
}

// Backend DTO: BulkDeleteCategoryResponseDto
export interface BulkDeleteCategoryResponseDto {
  deleted: number;
  message: string;
  deletedIds: string[];
}

// Backend DTO: BulkUpdateCategoryHierarchyDto
export interface BulkUpdateCategoryHierarchyDto {
  updates: Array<{
    id: string;
    parentId: string | null;
  }>;
}

// Backend DTO: BulkUpdateCategoryHierarchyResponseDto
export interface BulkUpdateCategoryHierarchyResponseDto {
  updated: number;
  failed: Array<{
    id: string;
    error: string;
  }>;
}

// Backend DTO: BulkUpdateCategoryStatusDto
export interface BulkUpdateCategoryStatusDto {
  categoryIds: string[];
  status: CategoryStatus;
}

// Backend DTO: BulkUpdateCategoryStatusResponseDto
export interface BulkUpdateCategoryStatusResponseDto {
  updated: number;
  message: string;
  updatedIds: string[];
  failed?: Array<{
    categoryId: string;
    error: string;
  }>;
}

// API Response wrappers
export interface CategoryResponse extends ApiResponse<CategoryDto> {}

export interface CategoryTableResponse
  extends ApiResponse<CategoryTableData[]> {}

export interface CategoryPaginatedResponse
  extends ApiResponse<PaginatedCategoriesResponseDto | null> {}

export interface SimpleCategoryResponse
  extends ApiResponse<CategorySlimDto[]> {}

export interface CategoryNameAvailabilityResponse
  extends ApiResponse<CategoryNameAvailabilityResponseDto> {}

export interface CategorySlugAvailabilityResponse
  extends ApiResponse<CategoryNameAvailabilityResponseDto> {}

export interface CategoryShortCodeAvailabilityResponse
  extends ApiResponse<CategoryNameAvailabilityResponseDto> {}

export interface CategoryIdResponse
  extends ApiResponse<CategoryIdResponseDto> {}

export interface BulkCategoryIdsResponse
  extends ApiResponse<BulkCategoryIdsResponseDto> {}

export interface BulkDeleteCategoryResponse
  extends ApiResponse<BulkDeleteCategoryResponseDto> {}

export interface BulkUpdateCategoryHierarchyResponse
  extends ApiResponse<BulkUpdateCategoryHierarchyResponseDto> {}

export interface BulkUpdateCategoryStatusResponse
  extends ApiResponse<BulkUpdateCategoryStatusResponseDto> {}

// Table data interface - optimized for table display
export interface CategoryTableData {
  id: string;
  name: string;
  shortCode?: string;
  status: CategoryStatus;
  availableOnline: boolean;
  position: number;
  color?: string;
  slug?: string;
  parentId?: string;
  parentName?: string;
  subcategoriesCount: number;
  productsCount: number;
  image?: string;
  // Location fields
  isAllocatedToAllLocations: boolean;
  locationIds: string[]; // Legacy field - will be removed after migration
  locations: { id: string; name: string }[]; // Empty array when allocated to all locations
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  ogImage?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Legacy aliases for backward compatibility (to be removed gradually)
export type Category = CategoryDto;
export type SimpleCategoryData = CategorySlimDto;
export type CategoryPaginatedData = PaginatedCategoriesResponseDto;

// Form values interface for UI components
export interface CategoryFormValues {
  name: string;
  shortCode?: string;
  parentId?: string;
  description?: string;
  slug?: string;
  availableOnline?: boolean;
  color?: string;
  status?: CategoryStatus;
  // Location fields
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[]; // For junction table management (ignored if isAllocatedToAllLocations is true)
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

// Extended interfaces for frontend use
export interface CategoryTableDataExtended extends CategoryTableData {
  _id: string; // Legacy field for backward compatibility
  publicId: string; // Legacy field for backward compatibility
  numProducts?: number; // Legacy field - use productsCount instead
  code?: string; // Legacy field mapping to shortCode
  customFields?: Record<string, string>[]; // Legacy field
  availableInOnlineStore: boolean; // Legacy field mapping to availableOnline
  parent?: CategorySlimDto; // Parent category details
  subcategories?: CategorySlimDto[]; // Subcategory details
}

// Bulk create category interface
export interface BulkCreateCategoryDto extends CreateCategoryDto {
  imageIndex?: number; // For mapping to uploaded images
}

// Hierarchy data interface - optimized for hierarchy display
export interface CategoryHierarchyData {
  id: string;
  name: string;
  parentId?: string;
  position: number;
}

// API Response wrapper for hierarchy data
export interface CategoryHierarchyResponse
  extends ApiResponse<CategoryHierarchyData[]> {}
