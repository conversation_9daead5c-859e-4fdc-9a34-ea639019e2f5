import { ApiResponse } from "./common";

// Enums matching backend schema
export enum AssetType {
  IT_EQUIPMENT = 'it_equipment',
  FURNITURE = 'furniture',
  VEHICLE = 'vehicle',
  MACHINERY = 'machinery',
  BUILDING = 'building',
  OFFICE_EQUIPMENT = 'office_equipment',
  SOFTWARE = 'software',
  OTHER = 'other',
}

export enum MaintenanceFrequency {
  DAYS = 'days',
  WEEKS = 'weeks',
  MONTHS = 'months',
  YEARS = 'years',
}

export enum AssetReference {
  VEHICLE = 'vehicle',
  ACCOMMODATION = 'accommodation',
  EVENT_SPACE = 'event_space',
  RENTAL_ITEM = 'rental_item',
}

export enum AssetStatus {
  AVAILABLE = "available",
  UNAVAILABLE = "unavailable",
  ASSIGNED = "assigned",
  DISCARDED = "discarded",
  LOST = "lost",
  STOLEN = "stolen",
  DAMAGED = "damaged",
  MAINTENANCE = "maintenance",
  ALLOCATED = "allocated",
}

// Backend DTO: AssetDto
export interface AssetDto {
  id: string;
  businessId: string;
  assetCode: string;
  name: string;
  type: AssetType;
  
  // Supplier Details
  supplierId?: string;
  
  purchaseDate?: string;
  purchasePrice?: string;
  purchaseOrderNumber?: string;
  
  // Maintenance Details
  maintenanceFrequencyValue?: number;
  maintenanceFrequency?: MaintenanceFrequency;
  lastMaintenanceDate?: string;
  maintenanceDueDate?: string;
  warrantyExpiryDate?: string;
  warrantyPeriod?: string;
  lifecycleExpiryDate?: string;
  
  // Dynamic fields based on asset type
  key1?: string;
  key2?: string;
  key3?: string;
  key4?: string;
  key5?: string;
  key6?: string;
  key7?: string;
  key8?: string;
  key9?: string;
  key10?: string;
  
  categoryId?: string;
  subCategoryId?: string;
  isAllocatedToAllLocations?: boolean;
  bookValue?: string;
  
  // Accounting Accounts
  fixedAssetAccountId?: string;
  depreciationAccountId?: string;
  expenseAccountId?: string;
  
  description?: string;
  notes?: string;
  
  // Reference fields
  reference?: AssetReference;
  referenceId?: string;
  
  status: AssetStatus;
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

// Backend DTO: CreateAssetDto
export interface CreateAssetDto {
  assetCode: string;
  name: string;
  type?: AssetType;
  
  // Supplier Details
  supplierId?: string;
  
  purchaseDate?: string;
  purchasePrice?: string;
  purchaseOrderNumber?: string;
  
  // Maintenance Details
  maintenanceFrequencyValue?: number;
  maintenanceFrequency?: MaintenanceFrequency;
  lastMaintenanceDate?: string;
  maintenanceDueDate?: string;
  warrantyExpiryDate?: string;
  warrantyPeriod?: string;
  lifecycleExpiryDate?: string;
  
  // Dynamic fields based on asset type
  key1?: string;
  key2?: string;
  key3?: string;
  key4?: string;
  key5?: string;
  key6?: string;
  key7?: string;
  key8?: string;
  key9?: string;
  key10?: string;
  
  categoryId?: string;
  subCategoryId?: string;
  isAllocatedToAllLocations?: boolean;
  bookValue?: string;
  
  // Accounting Accounts
  fixedAssetAccountId?: string;
  depreciationAccountId?: string;
  expenseAccountId?: string;
  
  description?: string;
  notes?: string;
  
  // Reference fields
  reference?: AssetReference;
  referenceId?: string;
  
  status?: AssetStatus;
}

// Backend DTO: UpdateAssetDto
export interface UpdateAssetDto {
  assetCode?: string;
  name?: string;
  type?: AssetType;
  
  // Supplier Details
  supplierId?: string;
  
  purchaseDate?: string;
  purchasePrice?: string;
  purchaseOrderNumber?: string;
  
  // Maintenance Details
  maintenanceFrequencyValue?: number;
  maintenanceFrequency?: MaintenanceFrequency;
  lastMaintenanceDate?: string;
  maintenanceDueDate?: string;
  warrantyExpiryDate?: string;
  warrantyPeriod?: string;
  lifecycleExpiryDate?: string;
  
  // Dynamic fields based on asset type
  key1?: string;
  key2?: string;
  key3?: string;
  key4?: string;
  key5?: string;
  key6?: string;
  key7?: string;
  key8?: string;
  key9?: string;
  key10?: string;
  
  categoryId?: string;
  subCategoryId?: string;
  isAllocatedToAllLocations?: boolean;
  bookValue?: string;
  
  // Accounting Accounts
  fixedAssetAccountId?: string;
  depreciationAccountId?: string;
  expenseAccountId?: string;
  
  description?: string;
  notes?: string;
  
  // Reference fields
  reference?: AssetReference;
  referenceId?: string;
  
  status?: AssetStatus;
}

// Backend DTO: AssetSlimDto
export interface AssetSlimDto {
  id: string;
  name: string;
  assetCode: string;
}

// Backend DTO: PaginationMeta - Updated to match backend
export interface PaginationMeta {
  total: number;
  page: number;
  totalPages: number;
}

// Legacy interface for backward compatibility
export interface PaginationMetaDto extends PaginationMeta {}

export interface PaginatedAssetsResponseDto {
  data: AssetTableData[];
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

// Backend DTO: DeleteAssetResponseDto
export interface DeleteAssetResponseDto {
  id: string;
  message: string;
}

// Backend DTO: AssetNameAvailabilityResponseDto
export interface AssetNameAvailabilityResponseDto {
  available: boolean;
  name: string;
}

// Backend DTO: AssetIdResponseDto
export interface AssetIdResponseDto {
  id: string;
}

// Backend DTO: BulkAssetIdsResponseDto
export interface BulkAssetIdsResponseDto {
  ids: string[];
}

// Backend DTO: BulkDeleteAssetDto
export interface BulkDeleteAssetDto {
  ids: string[];
}

// Backend DTO: BulkDeleteAssetResponseDto
export interface BulkDeleteAssetResponseDto {
  deletedCount: number;
  deletedIds: string[];
}

// Backend DTO: BulkUpdateAssetStatusDto
export interface BulkUpdateAssetStatusDto {
  ids: string[];
  status: AssetStatus;
}

// Backend DTO: BulkUpdateAssetStatusResponseDto
export interface BulkUpdateAssetStatusResponseDto {
  updated: number;
  updatedIds: string[];
  message: string;
}

// API Response wrappers
export interface AssetResponse extends ApiResponse<AssetDto> {}

export interface AssetTableResponse extends ApiResponse<AssetTableData[]> {}

export interface AssetPaginatedResponse
  extends ApiResponse<PaginatedAssetsResponseDto | null> {}

export interface SimpleAssetResponse extends ApiResponse<AssetSlimDto[]> {}

export interface AssetNameAvailabilityResponse
  extends ApiResponse<AssetNameAvailabilityResponseDto> {}

export interface AssetIdResponse extends ApiResponse<AssetIdResponseDto> {}

export interface BulkAssetIdsResponse
  extends ApiResponse<BulkAssetIdsResponseDto> {}

export interface BulkDeleteAssetResponse
  extends ApiResponse<BulkDeleteAssetResponseDto> {}

export interface BulkUpdateAssetStatusResponse
  extends ApiResponse<BulkUpdateAssetStatusResponseDto> {}

// Table data interface - optimized for table display
export interface AssetTableData {
  id: string;
  assetCode: string;
  name: string;
  type: AssetType;
  status: AssetStatus;
  categoryId?: string;
  subCategoryId?: string;
  supplierId?: string;
  purchaseDate?: string;
  purchasePrice?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Legacy aliases for backward compatibility (to be removed gradually)
export type Asset = AssetDto;
export type SimpleAssetData = AssetSlimDto;
export type AssetPaginatedData = PaginatedAssetsResponseDto;

// Form values interface for UI components
export interface AssetFormValues {
  assetCode: string;
  name: string;
  type?: AssetType;
  
  // Supplier Details
  supplierId?: string;
  
  purchaseDate?: string;
  purchasePrice?: string;
  purchaseOrderNumber?: string;
  
  // Maintenance Details
  maintenanceFrequencyValue?: number;
  maintenanceFrequency?: MaintenanceFrequency;
  lastMaintenanceDate?: string;
  maintenanceDueDate?: string;
  warrantyExpiryDate?: string;
  warrantyPeriod?: string;
  lifecycleExpiryDate?: string;
  
  // Dynamic fields based on asset type
  key1?: string;
  key2?: string;
  key3?: string;
  key4?: string;
  key5?: string;
  key6?: string;
  key7?: string;
  key8?: string;
  key9?: string;
  key10?: string;
  
  categoryId?: string;
  subCategoryId?: string;
  isAllocatedToAllLocations?: boolean;
  bookValue?: string;
  
  // Accounting Accounts
  fixedAssetAccountId?: string;
  depreciationAccountId?: string;
  expenseAccountId?: string;
  
  description?: string;
  notes?: string;
  
  // Reference fields
  reference?: AssetReference;
  referenceId?: string;
  
  status?: AssetStatus;
}

// Extended interfaces for frontend use
export interface AssetTableDataExtended extends AssetTableData {
  _id: string; // Legacy field for backward compatibility
  publicId: string; // Legacy field for backward compatibility
  code?: string; // Legacy field mapping to assetCode
  customFields?: Record<string, string>[]; // Legacy field
}

// Bulk create asset interface
export interface BulkCreateAssetDto extends CreateAssetDto {
  imageIndex?: number; // For mapping to uploaded images
}

// New interfaces for image and attachment management
export interface AssetImageDto {
  id: string;
  assetId: string;
  imageId: string;
  sortOrder: number;
  isPrimary: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateAssetImageDto {
  url: string;
  sortOrder: number;
  isPrimary?: boolean;
}

export interface UpdateAssetImagesSortOrderDto {
  images: { id: string; sortOrder: number }[];
}

export interface AssetAttachmentDto {
  id: string;
  fileName: string;
  originalName: string;
  publicUrl: string;
  size: number;
  mimeType: string;
  mediaType: string;
  uploadedAt: string;
}

// Backend DTO: PaginatedAssetsOptimizedResponseDto - Updated to match backend
export interface PaginatedAssetsOptimizedResponseDto {
  data: AssetListDto[];
  meta: PaginationMeta;
}

// Backend DTO: AssetListDto - Updated to match backend
export interface AssetListDto {
  id: string;
  assetCode: string;
  name: string;
  type: AssetType;
  status: AssetStatus;
  categoryId?: string;
  categoryName?: string;
  subCategoryId?: string;
  subCategoryName?: string;
  supplierId?: string;
  supplierName?: string;
  purchaseDate?: string;
  purchasePrice?: string;
  bookValue?: string;
  isAllocatedToAllLocations: boolean;
  locationsCount: number;
  image?: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Check asset name DTO
export interface CheckAssetNameDto {
  name: string;
  excludeId?: string;
}

export interface CheckAssetNameResponseDto {
  available: boolean;
  message: string;
}
